#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试边界检查修复的脚本
验证所有文字都不会超过色块10%的边界
"""

import os
import sys
import random
from pathlib import Path

# 添加项目根目录到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
sys.path.append(project_root)

from nature_colorblock_generate_script import (
    calculate_text_positions_in_colorblock,
    generate_colorblock_shape,
    collect_font_paths,
    sample_fonts,
    generate_text_lines,
    load_font_from_tif
)

def test_boundary_check():
    """测试边界检查逻辑"""
    print("=" * 60)
    print("测试色块边界检查修复")
    print("=" * 60)
    
    # 模拟色块参数
    image_size = (800, 600)
    cb_x, cb_y = 100, 100
    cb_width, cb_height = 300, 200
    
    print(f"色块位置: ({cb_x}, {cb_y})")
    print(f"色块尺寸: {cb_width} x {cb_height}")
    print(f"10%边界缓冲区: {cb_width * 0.1:.1f} x {cb_height * 0.1:.1f}")
    print(f"有效文字区域: {cb_width * 0.8:.1f} x {cb_height * 0.8:.1f}")
    
    # 收集字体
    font_paths = collect_font_paths()
    if not any(font_paths.values()):
        print("❌ 未找到字体文件，跳过测试")
        return
    
    # 抽样字体
    fonts_type = sample_fonts(font_paths, 2)
    font_sizes = [60, 60]  # 使用较大字号来测试边界
    
    # 加载字体
    fonts = []
    for i in range(2):
        try:
            font_path = fonts_type[i][1]
            font_size = font_sizes[i]
            font = load_font_from_tif(font_path, font_size)
            fonts.append([fonts_type[i][0], font])
        except Exception as e:
            print(f"❌ 加载字体失败: {e}")
            return
    
    # 生成文本行
    lines = generate_text_lines(2, 4, fonts)  # 2行，每行4个字符
    
    print(f"\n生成的文本:")
    for i, line in enumerate(lines):
        chars_text = ''.join([char['char'] for char in line['chars']])
        print(f"  第{i+1}行: {chars_text}")
    
    # 测试两种边界策略
    for use_tight_fit in [False, True]:
        mode_name = "贴边模式" if use_tight_fit else "安全边距模式"
        print(f"\n--- 测试 {mode_name} ---")
        
        # 计算文字位置
        positioned_lines = calculate_text_positions_in_colorblock(
            lines, cb_x, cb_y, cb_width, cb_height,
            line_spacing=15, char_spacing=5, 
            shape_type='rounded_rect', use_tight_fit=use_tight_fit
        )
        
        if not positioned_lines:
            print(f"❌ {mode_name}: 无法放置任何文字")
            continue
        
        # 验证边界检查
        all_chars_valid = True
        total_chars = 0
        valid_chars = 0
        
        # 计算严格边界
        cb_margin_x = cb_width * 0.1
        cb_margin_y = cb_height * 0.1
        strict_left = cb_x + cb_margin_x
        strict_right = cb_x + cb_width - cb_margin_x
        strict_top = cb_y + cb_margin_y
        strict_bottom = cb_y + cb_height - cb_margin_y
        
        for line_idx, line in enumerate(positioned_lines):
            for char in line['chars']:
                total_chars += 1
                
                # 计算字符边界
                char_left = char['x']
                char_right = char['x'] + char['width']
                char_top = char['y'] - char['height']
                char_bottom = char['y']
                
                # 检查是否在严格边界内
                if (char_left >= strict_left and char_right <= strict_right and
                    char_top >= strict_top and char_bottom <= strict_bottom):
                    valid_chars += 1
                    print(f"  ✅ 字符 '{char['char']}' 在边界内")
                else:
                    all_chars_valid = False
                    print(f"  ❌ 字符 '{char['char']}' 超出边界!")
                    print(f"     字符边界: ({char_left:.1f},{char_top:.1f})-({char_right:.1f},{char_bottom:.1f})")
                    print(f"     严格边界: ({strict_left:.1f},{strict_top:.1f})-({strict_right:.1f},{strict_bottom:.1f})")
        
        print(f"  结果: {valid_chars}/{total_chars} 个字符在边界内")
        if all_chars_valid:
            print(f"  ✅ {mode_name}: 所有字符都在10%边界内!")
        else:
            print(f"  ❌ {mode_name}: 有字符超出10%边界!")
    
    print("\n" + "=" * 60)
    print("边界检查测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_boundary_check()
