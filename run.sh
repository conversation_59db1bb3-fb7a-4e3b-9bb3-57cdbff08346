#sh cmd_scripts/train_image_denoising/hsyq_lamav2_denoise_train_release_data_v7.sh
#sh cmd_scripts/train_image_denoising/hsyq_lamav2_denoise_train_release_data_v8.sh
#sh cmd_scripts/train_image_denoising/vis-hsyq_lamav2_denoise_train_release_data_v10.sh
#sh cmd_scripts/train_image_denoising/hsyq_lamav2_denoise_train_release_data_v10.sh
#sh cmd_scripts/train_image_denoising/docres_denoise_train_v1.sh
#sh cmd_scripts/train_image_denoising/hsyq_lamav2_denoise_train_release_data_v13.sh
#sh cmd_scripts/train_image_denoising/vis-hsyq_lamav2_denoise_train_release_data_v10.sh
#sh cmd_scripts/train_image_matting/hsyq_train_birefnet_distributed.sh
#sh cmd_scripts/train_kantu_classifiers/hsyq_kantu_classifier_large_train_distributed.sh

#sh cmd_scripts/train_image_inpainting/hsyq_lamav2_inpainting_train_all_size_release.sh # 全图消除
#sh cmd_scripts/train_image_inpainting/hsyq_lamav2_inpainting_train_release_crop.sh # crop 方式消除
#sh cmd_scripts/train_image_inpainting/hsyq_lamav2_inpainting_train_release_crop_run.sh # crop 方式消除
#sh cmd_scripts/train_image_inpainting/hsyq_lamav2_inpainting_train_release_crop_all_datav2.sh # crop 方式消除
sh cmd_scripts/train_image_inpainting/hsyq_lamav2_inpainting_train_release_test.sh # 消除命令