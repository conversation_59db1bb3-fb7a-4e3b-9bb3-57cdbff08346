# JIS X 0201 (1976) mappings for ISO 2022 usage.

# Invoke JIS Roman in G0/GL, and JIS Katakana ("halfwidth" katakana, but
# we map it to fullwidth for compatibility with existing fonts) in
# G1/GR.
#
# This means that the Katakana is available in the high bytes, or you
# can use SHIFT OUT (0x1E) to cause the low-value bytes (in the ASCII
# code value range) to invoke Katakana characters (using SHIFT IN (0x1F)
# to switch back). Of course, this being ISO 2022, you can also use the
# escape sequences ESC ( I and ESC ( J to shift it in and out.

g 0 94 J
g 1 94 I
g L 0
g R 1

# JIS Roman: ISO 646 variant just like ASCII, but with <Yen> for
# <backslash>, and <overline> for <tilde>.

0x4A0020    0x20
0x4A0021    0x21
0x4A0022    0x22
0x4A0023    0x23
0x4A0024    0x24
0x4A0025    0x25
0x4A0026    0x26
0x4A0027    0x27
0x4A0028    0x28
0x4A0029    0x29
0x4A002A    0x2A
0x4A002B    0x2B
0x4A002C    0x2C
0x4A002D    0x2D
0x4A002E    0x2E
0x4A002F    0x2F
0x4A0030    0x30
0x4A0031    0x31
0x4A0032    0x32
0x4A0033    0x33
0x4A0034    0x34
0x4A0035    0x35
0x4A0036    0x36
0x4A0037    0x37
0x4A0038    0x38
0x4A0039    0x39
0x4A003A    0x3A
0x4A003B    0x3B
0x4A003C    0x3C
0x4A003D    0x3D
0x4A003E    0x3E
0x4A003F    0x3F
0x4A0040    0x40
0x4A0041    0x41
0x4A0042    0x42
0x4A0043    0x43
0x4A0044    0x44
0x4A0045    0x45
0x4A0046    0x46
0x4A0047    0x47
0x4A0048    0x48
0x4A0049    0x49
0x4A004A    0x4A
0x4A004B    0x4B
0x4A004C    0x4C
0x4A004D    0x4D
0x4A004E    0x4E
0x4A004F    0x4F
0x4A0050    0x50
0x4A0051    0x51
0x4A0052    0x52
0x4A0053    0x53
0x4A0054    0x54
0x4A0055    0x55
0x4A0056    0x56
0x4A0057    0x57
0x4A0058    0x58
0x4A0059    0x59
0x4A005A    0x5A
0x4A005B    0x5B
0x4A005C    0xA5    # \ -> Yen
0x4A005D    0x5D
0x4A005E    0x5E
0x4A005F    0x5F
0x4A0060    0x60
0x4A0061    0x61
0x4A0062    0x62
0x4A0063    0x63
0x4A0064    0x64
0x4A0065    0x65
0x4A0066    0x66
0x4A0067    0x67
0x4A0068    0x68
0x4A0069    0x69
0x4A006A    0x6A
0x4A006B    0x6B
0x4A006C    0x6C
0x4A006D    0x6D
0x4A006E    0x6E
0x4A006F    0x6F
0x4A0070    0x70
0x4A0071    0x71
0x4A0072    0x72
0x4A0073    0x73
0x4A0074    0x74
0x4A0075    0x75
0x4A0076    0x76
0x4A0077    0x77
0x4A0078    0x78
0x4A0079    0x79
0x4A007A    0x7A
0x4A007B    0x7B
0x4A007C    0x7C
0x4A007D    0x7D
0x4A007E    0x203E


# Mappings for JIS Katakana.

0x490021    0x3002
0x490022    0x300C
0x490023    0x300D
0x490024    0x3001
0x490025    0x30FB
0x490026    0x30F2
0x490027    0x30A1
0x490028    0x30A3
0x490029    0x30A5
0x49002A    0x30A7
0x49002B    0x30A9
0x49002C    0x30E3
0x49002D    0x30E5
0x49002E    0x30E7
0x49002F    0x30C3
0x490030    0x30FC
0x490031    0x30A2
0x490032    0x30A4
0x490033    0x30A6
0x490034    0x30A8
0x490035    0x30AA
0x490036    0x30AB
0x490037    0x30AD
0x490038    0x30AF
0x490039    0x30B1
0x49003A    0x30B3
0x49003B    0x30B5
0x49003C    0x30B7
0x49003D    0x30B9
0x49003E    0x30BB
0x49003F    0x30BD
0x490040    0x30BF
0x490041    0x30C1
0x490042    0x30C4
0x490043    0x30C6
0x490044    0x30C8
0x490045    0x30CA
0x490046    0x30CB
0x490047    0x30CC
0x490048    0x30CD
0x490049    0x30CE
0x49004A    0x30CF
0x49004B    0x30D2
0x49004C    0x30D5
0x49004D    0x30D8
0x49004E    0x30DB
0x49004F    0x30DE
0x490050    0x30DF
0x490051    0x30E0
0x490052    0x30E1
0x490053    0x30E2
0x490054    0x30E4
0x490055    0x30E6
0x490056    0x30E8
0x490057    0x30E9
0x490058    0x30EA
0x490059    0x30EB
0x49005A    0x30EC
0x49005B    0x30ED
0x49005C    0x30EF
0x49005D    0x30F3
0x49005E    0x309B
0x49005F    0x309C

# For reference, here's what they'd map to if we were mapping to
# halfwidth Katakana.
#
# 0x490021   0xFF61
# 0x490022   0xFF62
# 0x490023   0xFF63
# 0x490024   0xFF64
# 0x490025   0xFF65
# 0x490026   0xFF66
# 0x490027   0xFF67
# 0x490028   0xFF68
# 0x490029   0xFF69
# 0x49002A   0xFF6A
# 0x49002B   0xFF6B
# 0x49002C   0xFF6C
# 0x49002D   0xFF6D
# 0x49002E   0xFF6E
# 0x49002F   0xFF6F
# 0x490030   0xFF70
# 0x490031   0xFF71
# 0x490032   0xFF72
# 0x490033   0xFF73
# 0x490034   0xFF74
# 0x490035   0xFF75
# 0x490036   0xFF76
# 0x490037   0xFF77
# 0x490038   0xFF78
# 0x490039   0xFF79
# 0x49003A   0xFF7A
# 0x49003B   0xFF7B
# 0x49003C   0xFF7C
# 0x49003D   0xFF7D
# 0x49003E   0xFF7E
# 0x49003F   0xFF7F
# 0x490040   0xFF80
# 0x490041   0xFF81
# 0x490042   0xFF82
# 0x490043   0xFF83
# 0x490044   0xFF84
# 0x490045   0xFF85
# 0x490046   0xFF86
# 0x490047   0xFF87
# 0x490048   0xFF88
# 0x490049   0xFF89
# 0x49004A   0xFF8A
# 0x49004B   0xFF8B
# 0x49004C   0xFF8C
# 0x49004D   0xFF8D
# 0x49004E   0xFF8E
# 0x49004F   0xFF8F
# 0x490050   0xFF90
# 0x490051   0xFF91
# 0x490052   0xFF92
# 0x490053   0xFF93
# 0x490054   0xFF94
# 0x490055   0xFF95
# 0x490056   0xFF96
# 0x490057   0xFF97
# 0x490058   0xFF98
# 0x490059   0xFF99
# 0x49005A   0xFF9A
# 0x49005B   0xFF9B
# 0x49005C   0xFF9C
# 0x49005D   0xFF9D
# 0x49005E   0xFF9E
# 0x49005F   0xFF9F
