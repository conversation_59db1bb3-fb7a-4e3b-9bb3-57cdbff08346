#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/22
# <AUTHOR> Assistant
# @FileName: merge_crop_datasets.py
"""
合并新生成的crop数据集到现有的训练数据集中
"""

import os
import json
import shutil
from pathlib import Path

def backup_original_files(dataset_root):
    """备份原始的训练文件"""
    train_crop_path = os.path.join(dataset_root, "train_crop.jsonl")
    val_crop_path = os.path.join(dataset_root, "val_crop.jsonl")
    
    if os.path.exists(train_crop_path):
        backup_train_path = os.path.join(dataset_root, "train_crop_backup.jsonl")
        shutil.copy2(train_crop_path, backup_train_path)
        print(f"[Info] 已备份原始训练文件到: {backup_train_path}")
    
    if os.path.exists(val_crop_path):
        backup_val_path = os.path.join(dataset_root, "val_crop_backup.jsonl")
        shutil.copy2(val_crop_path, backup_val_path)
        print(f"[Info] 已备份原始验证文件到: {backup_val_path}")

def count_lines_in_file(file_path):
    """统计文件行数"""
    if not os.path.exists(file_path):
        return 0
    with open(file_path, 'r', encoding='utf-8') as f:
        return sum(1 for _ in f)

def merge_jsonl_files(original_file, new_file, output_file):
    """合并两个JSONL文件"""
    print(f"[Info] 正在合并文件:")
    print(f"  原始文件: {original_file}")
    print(f"  新数据文件: {new_file}")
    print(f"  输出文件: {output_file}")
    
    # 统计原始数据量
    original_count = count_lines_in_file(original_file)
    new_count = count_lines_in_file(new_file)
    
    print(f"  原始数据量: {original_count}")
    print(f"  新数据量: {new_count}")
    
    # 合并文件
    with open(output_file, 'w', encoding='utf-8') as output:
        # 先写入原始数据
        if os.path.exists(original_file):
            with open(original_file, 'r', encoding='utf-8') as original:
                for line in original:
                    output.write(line)
        
        # 再写入新数据
        if os.path.exists(new_file):
            with open(new_file, 'r', encoding='utf-8') as new:
                for line in new:
                    output.write(line)
    
    # 统计合并后的数据量
    merged_count = count_lines_in_file(output_file)
    print(f"  合并后数据量: {merged_count}")
    print(f"  验证: {original_count} + {new_count} = {merged_count}")
    
    return merged_count

def main():
    # 数据集根目录
    dataset_root = "/aicamera-mlp/hz_datasets/xiaochu_dataset"
    
    # 文件路径
    original_train_file = os.path.join(dataset_root, "train_crop.jsonl")
    original_val_file = os.path.join(dataset_root, "val_crop.jsonl")
    
    new_train_file = os.path.join(dataset_root, "train_crop_new_nature.jsonl")
    new_val_file = os.path.join(dataset_root, "val_crop_new_nature.jsonl")
    
    # 检查新文件是否存在
    if not os.path.exists(new_train_file):
        print(f"[Error] 新训练文件不存在: {new_train_file}")
        print("请先运行 create_dataset_crop_jsonl_mp.py 生成新数据集")
        return
    
    if not os.path.exists(new_val_file):
        print(f"[Error] 新验证文件不存在: {new_val_file}")
        print("请先运行 create_dataset_crop_jsonl_mp.py 生成新数据集")
        return
    
    print("=" * 60)
    print("开始合并数据集")
    print("=" * 60)
    
    # 备份原始文件
    backup_original_files(dataset_root)
    
    # 合并训练集
    print("\n[Step 1] 合并训练集...")
    train_merged_count = merge_jsonl_files(
        original_train_file, 
        new_train_file, 
        original_train_file
    )
    
    # 合并验证集
    print("\n[Step 2] 合并验证集...")
    val_merged_count = merge_jsonl_files(
        original_val_file, 
        new_val_file, 
        original_val_file
    )
    
    print("\n" + "=" * 60)
    print("合并完成！")
    print("=" * 60)
    print(f"最终训练集数据量: {train_merged_count}")
    print(f"最终验证集数据量: {val_merged_count}")
    print(f"总数据量: {train_merged_count + val_merged_count}")
    
    print("\n备份文件位置:")
    print(f"  原始训练集备份: {dataset_root}/train_crop_backup.jsonl")
    print(f"  原始验证集备份: {dataset_root}/val_crop_backup.jsonl")
    
    print("\n如果需要恢复原始数据集，可以运行:")
    print(f"  cp {dataset_root}/train_crop_backup.jsonl {dataset_root}/train_crop.jsonl")
    print(f"  cp {dataset_root}/val_crop_backup.jsonl {dataset_root}/val_crop.jsonl")

if __name__ == "__main__":
    main()
