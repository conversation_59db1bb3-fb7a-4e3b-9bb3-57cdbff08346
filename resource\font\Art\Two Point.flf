flf2a$ 2 2 8 0 14
twopoint by <PERSON> (<EMAIL>)
For figlet release 2.0
Date:  1994 Aug 11

Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be `a', for now
$    - the "hardblank" -- prints as a blank, but can't be smushed
2    - height of a character
2    - height of a character, not including descenders
8    - max line length (excluding comment lines)
0    - default smushmode for this font (like "-m 15" on command line)
14   - number of comment lines

$$@
$$@@
|@
o@@
''@
$$@@
++@
++@@
(|~@
_|)@@
o/@
/o@@
 o @
(_X@@
)@
$@@
/~@
\_@@
~\@
_/@@
\|/@
/|\@@
$|$@
~|~@@
$@
)@@
$$@
~~@@
$@
o@@
$/@
/$@@
/\@
\/@@
'|@
$|@@
~)@
/_@@
~/@
_)@@
/|$@
~|~@@
|~@
_)@@
 / @
(_)@@
~/@
/$@@
(~)@
(_)@@
(~|@
$/$@@
o@
o@@
o@
)@@
/@
\@@
--@
--@@
\@
/@@
~)@
o$@@
 _ @
(a)@@
|~|@
|~|@@
|~)@
|_)@@
|~@
|_@@
|~\@
|_/@@
[~@
[_@@
|~@
|~@@
|~_@
|_|@@
|_|@
| |@@
|@
|@@
$|@
_|@@
|/@
|\@@
|$@
|_@@
|\/|@
|  |@@
|\ |@
| \|@@
/~\@
\_/@@
|~)@
|~ @@
/~\@
\_X@@
|~)@
|~\@@
(~@
_)@@
~|~@
$|$@@
| |@
|_|@@
\  /@
$\/$@@
|    |@
$\/\/$@@
\/@
/\@@
\/@
/$@@
~/@
/_@@
|~@
|_@@
\$@
$\@@
~|@
_|@@
/\@
$$@@
$$@
__@@
(@
 @@
$_$@
(_|@@
|_$@
|_)@@
$_@
(_@@
$_|@
(_|@@
$_@
}_@@
$|~@
~|~@@
(~|@
$_|@@
|_$@
| |@@
o@
|@@
$o@
_|@@
|$@
|<@@
|@
|@@
._ _$@
| | |@@
._$@
| |@@
$_$@
(_)@@
|)@
|$@@
(|@
$|@@
._@
|$@@
$_@
_\@@
_|_@
$|$@@
$ $@
|_|@@
$$@
\/@@
$  $@
\/\/@@
$$@
><@@
|_|@
$_|@@
_$@
/_@@
$|~@
~|_@@
|@
|@@
~|$@
_|~@@
/\/@
$ $@@
o~o@
|~|@@
o~o@
\_/@@
q p@
|_|@@
o_o@
(_|@@
o_o@
(_)@@
o o@
|_|@@
|~)@
| )@@
