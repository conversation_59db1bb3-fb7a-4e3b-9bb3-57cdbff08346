# 数据集制作规范说明文档

## 概述

本文档详细说明了为HSYQ LaMa V2 Inpainting训练系统制作数据集的标准规范。严格按照此规范制作的数据集可以直接用于训练，确保系统的稳定性和性能。

## 1. 目录结构规范

### 1.1 标准目录结构

每个数据集必须按照以下标准结构组织：

```
数据集根目录/
├── 数据集类型名称1/
│   ├── gt/           # 必需：Ground Truth 真值图像
│   ├── im/           # 必需：Input Image 输入图像  
│   ├── mask/         # 必需：Mask 掩码图像
│   ├── input/        # 可选：备用输入目录
│   ├── show/         # 可选：可视化展示图像
│   └── image_mapping.json  # 可选：图像元数据文件
├── 数据集类型名称2/
│   ├── gt/
│   ├── im/
│   ├── mask/
│   └── ...
└── alldata_clear_json/  # 必需：元数据目录
    ├── train.jsonl      # 必需：训练集元数据
    ├── val.jsonl        # 必需：验证集元数据
    ├── train_crop.jsonl # 可选：裁剪训练集元数据
    ├── val_crop.jsonl   # 可选：裁剪验证集元数据
    ├── train_debug.jsonl # 可选：调试训练集
    └── val_debug.jsonl   # 可选：调试验证集
```

### 1.2 目录命名规范

**数据集类型名称要求**:
- 使用描述性英文名称，如：`doc_enhance_v1`, `table_uvdoc`, `nature_dataset_enhance`
- 避免使用特殊字符和空格
- 建议使用下划线分隔单词
- 包含版本号便于管理

**示例**:
```
doc_enhance_v1          # 文档增强数据集版本1
table_uvdoc            # 表格UV文档数据集
nature_dataset_enhance # 自然场景数据集增强版
doc_ch_en1_filter_enhance # 中英文档过滤增强版
```

## 2. 图像文件规范

### 2.1 支持的图像格式

**代码支持的格式** (基于 `modules/utils/image_utils.py` 第25行):
```python
IMAGE_FMT = ['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']
```

**推荐格式**:
- **主要格式**: `.jpg` (JPEG格式，适合自然图像)
- **备选格式**: `.png` (PNG格式，适合需要透明度或无损压缩的图像)

**格式选择建议**:
- 自然场景图像：使用 `.jpg` 格式，质量设置85-95%
- 文档图像：使用 `.png` 格式保证清晰度
- 掩码图像：必须使用 `.png` 格式保证精确性

### 2.2 图像尺寸要求

**基本要求**:
- **最小尺寸**: 不小于 256x256 像素
- **推荐尺寸**: 512x512 到 2048x2048 像素之间
- **最大尺寸**: 不超过 4096x4096 像素（避免内存溢出）

**尺寸一致性**:
- 同一样本的 `im`、`mask`、`gt` 三张图像必须具有**完全相同的尺寸**
- 代码会自动检查尺寸一致性，不一致的样本会被跳过

**代码验证** (基于 `examples/inpainting/load_images` 函数):
```python
# 确保两张图像尺寸一致
if image.size != mask.size:
    print("Warning: Image and mask sizes do not match.")
    mask = mask.resize(image.size)
```

### 2.3 图像质量要求

**通用质量标准**:
- 图像清晰，无明显模糊或噪声
- 色彩自然，无明显色偏
- 避免过度压缩导致的伪影

**特定图像要求**:

**GT图像 (Ground Truth)**:
- 必须是完美修复后的目标图像
- 不能包含任何需要修复的缺陷
- 色彩和纹理自然连贯

**IM图像 (Input Image)**:
- 包含需要修复的原始图像
- 缺陷区域应该明显可见
- 与GT图像在非缺陷区域应该一致

**MASK图像 (Mask)**:
- 必须是单通道灰度图像 (L模式)
- 白色区域 (255) 表示需要修复的区域
- 黑色区域 (0) 表示保持不变的区域
- 边缘应该清晰，避免模糊过渡

## 3. 文件命名规范

### 3.1 图像文件命名

**命名原则**:
- 同一样本的三张图像必须使用**相同的文件名**（不包括扩展名）
- 文件名应该具有唯一性和描述性
- 避免使用特殊字符和中文

**命名格式建议**:
```
数据集前缀_序号_描述.扩展名

示例:
doc_001_invoice.jpg
doc_002_contract.jpg  
table_001_financial.png
```

**文件对应关系**:
```
数据集类型/im/sample_001.jpg     # 输入图像
数据集类型/mask/sample_001.png   # 对应掩码
数据集类型/gt/sample_001.jpg     # 对应真值
```

### 3.2 避免的命名模式

**禁止使用**:
- 中文文件名：`图像_001.jpg` ❌
- 特殊字符：`image@#$.jpg` ❌  
- 空格：`image 001.jpg` ❌
- 过长文件名：超过100个字符 ❌

## 4. 元数据文件规范

### 4.1 JSONL文件格式

**标准格式** (每行一个JSON对象):
```json
{"dataset_name": "doc_enhance_v1", "im": "im/image_001.jpg", "mask": "mask/mask_001.png", "gt": "gt/gt_001.jpg"}
{"dataset_name": "doc_enhance_v1", "im": "im/image_002.jpg", "mask": "mask/mask_002.png", "gt": "gt/gt_002.jpg"}
```

**字段说明**:
- `dataset_name`: 数据集类型名称，必须与目录名一致
- `im`: 输入图像的相对路径（相对于数据集类型目录）
- `mask`: 掩码图像的相对路径
- `gt`: 真值图像的相对路径

**裁剪数据集格式** (train_crop.jsonl, val_crop.jsonl):
```json
{"dataset_name": "doc_enhance_v1", "im": "im/image_001.jpg", "mask": "mask/mask_001.png", "gt": "gt/gt_001.jpg", "crop_index": [100, 100, 600, 600]}
```

额外字段:
- `crop_index`: 裁剪区域坐标 [left, top, right, bottom]

### 4.2 数据集划分比例

**推荐划分比例**:
- 训练集：80-90%
- 验证集：10-20%
- 测试集：可选，通常使用独立数据集

**文件数量建议**:
- 最小训练集：1000个样本
- 推荐训练集：10000个样本以上
- 验证集：训练集的10-20%

## 5. 数据质量检查

### 5.1 自动化检查工具

**使用现有工具** (`examples/inpainting/create_dataset_jsonl.py` 第111-116行):
```python
# 检查文件完整性 - 实际代码实现
if not os.path.exists(gt_image_path) or not os.path.exists(mask_image_path):
    if not os.path.exists(str(gt_image_path)):
        print(f'数据不完善:{str(gt_image_path)}')
    if not os.path.exists(str(mask_image_path)):
        print(f'数据不完善:{str(mask_image_path)}')
    continue
```

**实际检查项目**:
- ✅ 文件存在性检查 (第111-116行)
- ❌ 图像尺寸一致性检查 (代码中未实现)
- ❌ 图像格式有效性检查 (代码中未实现)
- ❌ JSONL格式正确性检查 (代码中未实现)

**注意**: 当前工具只检查文件存在性，其他检查需要手动实现或使用其他工具。

### 5.2 质量验证标准

**必须通过的检查**:
1. 所有样本都有对应的im、mask、gt文件
2. 同一样本的三张图像尺寸完全一致
3. 掩码图像为有效的灰度图像
4. GT图像在掩码区域外与IM图像一致
5. JSONL文件格式正确且路径有效

**推荐的额外检查**:
1. 掩码区域占比合理（5%-50%）
2. 图像质量符合要求（清晰度、色彩等）
3. 数据集内容多样性充足
4. 避免重复或相似样本过多

## 6. 数据集制作工具和流程

### 6.1 推荐制作流程

**步骤1: 准备原始数据**
1. 收集高质量的原始图像
2. 确保图像格式符合要求
3. 按数据集类型分类整理

**步骤2: 创建目录结构**
```bash
# 创建标准目录结构
mkdir -p 数据集根目录/数据集类型名称/{gt,im,mask,input,show}
mkdir -p 数据集根目录/alldata_clear_json
```

**步骤3: 生成图像三元组**
1. 创建输入图像 (im): 在原始图像上添加需要修复的缺陷
2. 创建掩码图像 (mask): 标记需要修复的区域
3. 创建真值图像 (gt): 使用完美修复后的图像

**步骤4: 生成元数据文件**
使用提供的工具自动生成JSONL文件：
```bash
python examples/inpainting/create_dataset_jsonl.py
```

**步骤5: 质量检查和验证**
1. 运行自动化检查工具
2. 人工抽样检查数据质量
3. 修复发现的问题

### 6.2 现有制作工具

**JSONL生成工具**:
- 位置: `examples/inpainting/create_dataset_jsonl.py`
- 功能: 扫描指定数据集的 `im/` 目录下的 `.png` 文件，生成JSONL元数据文件
- 扫描方式: 使用 `glob.glob(f'{dataset_dir}/im/**.png')` (第93行)
- **重要限制**: 当前脚本只扫描 `.png` 格式文件，如需支持其他格式需要修改代码
- 使用方法: 修改脚本中的 `dataset_root` 和 `dataset_names` 配置后运行

**多进程版本**:
- 位置: `examples/inpainting/create_dataset_jsonl_mp.py`
- 适用: 大规模数据集的快速处理

**裁剪数据集工具**:
- 位置: `examples/inpainting/create_dataset_crop_jsonl.py`
- 功能: 生成包含裁剪信息的JSONL文件

**数据可视化工具**:
- 位置: `examples/inpainting/visual_show.py`
- 功能: 批量可视化数据集样本

**数据清理工具**:
- 位置: `examples/inpainting/detele_folder.py`
- 功能: 批量删除有问题的样本

### 6.3 自定义制作脚本模板

**基础制作脚本**:
```python
#!/usr/bin/python3
import os
import json
from pathlib import Path
from PIL import Image
from tqdm import tqdm

def create_dataset(source_dir, output_dir, dataset_name):
    """
    数据集制作模板函数

    Args:
        source_dir: 原始数据目录
        output_dir: 输出数据集目录
        dataset_name: 数据集类型名称
    """
    # 创建目录结构
    dataset_dir = Path(output_dir) / dataset_name
    for subdir in ['gt', 'im', 'mask']:
        (dataset_dir / subdir).mkdir(parents=True, exist_ok=True)

    # 处理每个样本
    samples = []
    for i, source_file in enumerate(tqdm(source_files)):
        # 1. 生成输入图像 (im)
        im_image = process_input_image(source_file)
        im_path = dataset_dir / 'im' / f'{dataset_name}_{i:06d}.jpg'
        im_image.save(im_path)

        # 2. 生成掩码图像 (mask)
        mask_image = generate_mask(source_file)
        mask_path = dataset_dir / 'mask' / f'{dataset_name}_{i:06d}.png'
        mask_image.save(mask_path)

        # 3. 生成真值图像 (gt)
        gt_image = generate_ground_truth(source_file)
        gt_path = dataset_dir / 'gt' / f'{dataset_name}_{i:06d}.jpg'
        gt_image.save(gt_path)

        # 4. 记录样本信息
        sample = {
            "dataset_name": dataset_name,
            "im": f"im/{im_path.name}",
            "mask": f"mask/{mask_path.name}",
            "gt": f"gt/{gt_path.name}"
        }
        samples.append(sample)

    return samples

def generate_jsonl_files(samples, output_dir, train_ratio=0.8):
    """生成JSONL元数据文件"""
    import random
    random.shuffle(samples)

    split_idx = int(len(samples) * train_ratio)
    train_samples = samples[:split_idx]
    val_samples = samples[split_idx:]

    # 创建元数据目录
    json_dir = Path(output_dir) / 'alldata_clear_json'
    json_dir.mkdir(exist_ok=True)

    # 写入训练集
    with open(json_dir / 'train.jsonl', 'w', encoding='utf-8') as f:
        for sample in train_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')

    # 写入验证集
    with open(json_dir / 'val.jsonl', 'w', encoding='utf-8') as f:
        for sample in val_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')

    print(f"生成完成: 训练集{len(train_samples)}个样本, 验证集{len(val_samples)}个样本")
```

## 7. 特殊数据集类型

### 7.1 裁剪数据集 (Crop Dataset)

**适用场景**: 处理大图像中的局部区域

**额外要求**:
- 必须在JSONL中包含 `crop_index` 字段
- 坐标格式: `[left, top, right, bottom]`
- 坐标必须在原图像范围内

**示例**:
```json
{"dataset_name": "doc_crop_v1", "im": "im/doc_001.jpg", "mask": "mask/doc_001.png", "gt": "gt/doc_001.jpg", "crop_index": [100, 100, 600, 600]}
```

**代码处理** (基于 `my_datasets/image_inpainting/base_dataset_v2.py`):
```python
crop_index = data['crop_index']
im_image = im_image.crop(crop_index)
mask_image = mask_image.crop(crop_index)
gt_image = gt_image.crop(crop_index)
```

### 7.2 调试数据集 (Debug Dataset)

**用途**: 快速测试和调试

**文件命名**:
- `train_debug.jsonl`
- `val_debug.jsonl`

**特点**:
- 样本数量较少（通常100-1000个）
- 包含典型和边界情况样本
- 便于快速验证代码正确性

### 7.3 HSV增强数据集

**支持HSV颜色增强的数据集类型** (基于 `my_datasets/image_inpainting/base_dataset_v1.py` 第162-166行):
```python
allow_hsv_data_name = [
    'doc_ch_en1_filter_enhance',
    'doc_ch_en1_filter2_enhance',
    'doc_ch_en1_filter_uvdoc',
    'doc_ch_en1_filter2_uvdoc',
    'table_enhance',
    'table_uvdoc',
    'nature_dataset_enhance'
]
```

**HSV增强实现** (第167-169行):
```python
# 加入颜色增强方式 概率50%
if self.mode == 'train' and data_name in allow_hsv_data_name and random.random() < 0.5:
    im_image, mask_image, gt_image = random_hsv_pair([im_image, mask_image, gt_image])
```

**要求**: 这些数据集在训练时会自动应用HSV颜色增强，需要确保图像质量能够承受颜色变换。

## 8. 常见问题和解决方案

### 8.1 文件完整性问题

**问题**: 部分样本缺少对应的mask或gt文件

**解决方案**:
```python
# 使用自动检查工具
if not os.path.exists(gt_image_path) or not os.path.exists(mask_image_path):
    print(f'数据不完善:{gt_image_path} 或 {mask_image_path}')
    continue  # 跳过不完整的样本
```

### 8.2 图像尺寸不一致

**问题**: 同一样本的三张图像尺寸不匹配

**解决方案**:
1. 重新制作确保尺寸一致
2. 或使用代码自动调整:
```python
if image.size != mask.size:
    mask = mask.resize(image.size, Image.NEAREST)  # 掩码使用最近邻插值
```

### 8.3 掩码格式错误

**问题**: 掩码不是单通道灰度图像

**解决方案**:
```python
# 确保掩码为灰度图像
mask_image = Image.open(mask_path).convert("L")
```

### 8.4 JSONL格式错误

**问题**: JSON格式不正确或编码问题

**解决方案**:
1. 使用UTF-8编码保存文件
2. 确保每行都是有效的JSON对象
3. 使用工具验证JSON格式:
```python
import json
with open('train.jsonl', 'r', encoding='utf-8') as f:
    for line_num, line in enumerate(f, 1):
        try:
            json.loads(line.strip())
        except json.JSONDecodeError as e:
            print(f"第{line_num}行JSON格式错误: {e}")
```

## 9. 性能优化建议

### 9.1 存储优化

**图像压缩**:
- JPEG质量设置85-95%平衡质量和大小
- PNG使用适当的压缩级别
- 避免不必要的高分辨率

**目录组织**:
- 避免单个目录下文件过多（建议不超过10000个）
- 可以按子目录分组组织大数据集

### 9.2 加载性能

**文件格式选择**:
- 训练时优先使用JPEG格式（加载速度快）
- 掩码必须使用PNG保证精度

**预处理优化**:
- 考虑预先调整图像到训练尺寸
- 使用多进程数据加载器

## 10. 验收标准

### 10.1 必须满足的条件

1. ✅ 目录结构完全符合规范
2. ✅ 所有样本都有完整的im、mask、gt文件
3. ✅ 图像格式在支持列表内
4. ✅ 同一样本三张图像尺寸完全一致
5. ✅ JSONL文件格式正确且路径有效
6. ✅ 掩码图像为有效的单通道灰度图
7. ✅ 文件命名符合规范，无特殊字符

### 10.2 推荐达到的标准

1. 🔶 数据质量高，图像清晰无伪影
2. 🔶 样本多样性丰富，覆盖各种场景
3. 🔶 训练/验证集划分合理
4. 🔶 包含适量的调试数据集
5. 🔶 提供数据集说明文档

### 10.3 验收测试

**自动化测试**:
```bash
# 运行JSONL生成工具（会自动检查文件完整性）
python examples/inpainting/create_dataset_jsonl.py

# 运行可视化工具检查样本质量（需要修改脚本内部的folder_list配置）
python examples/inpainting/visual_show.py
```

**注意**: 当前的 `create_dataset_jsonl.py` 脚本没有 `--check-only` 参数，需要通过修改脚本内部的配置来控制行为。脚本在处理过程中会自动检查文件完整性：

```python
# 脚本会自动检查文件完整性
if not os.path.exists(gt_image_path) or not os.path.exists(mask_image_path):
    print(f'数据不完善:{gt_image_path} 或 {mask_image_path}')
    continue
```

**使用现有工具的正确方法**:

1. **JSONL生成和检查**:
   ```python
   # 修改 examples/inpainting/create_dataset_jsonl.py 中的配置
   dataset_root = "/path/to/your/dataset"  # 修改为你的数据集路径
   product_mode = False  # 设置为False使用debug模式进行测试

   # 修改数据集列表
   dataset_names = [
       ("your_dataset_name", 100),  # 先用少量数据测试
   ]
   ```

2. **可视化检查**:
   ```python
   # 修改 examples/inpainting/visual_show.py 中的配置
   folder_list = [
       "/path/to/your/dataset/dataset_type/",  # 修改为你的数据集路径
   ]
   num_samples = 50  # 设置采样数量
   ```

**手工验收**:
1. 随机抽取100个样本进行人工检查
2. 确认掩码区域标注准确
3. 验证GT图像修复效果自然
4. 检查是否存在明显的数据质量问题

按照此规范制作的数据集可以直接用于HSYQ LaMa V2 Inpainting系统的训练，确保训练过程的稳定性和最终模型的性能。
