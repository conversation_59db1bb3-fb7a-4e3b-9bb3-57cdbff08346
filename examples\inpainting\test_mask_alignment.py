#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试mask对齐修复效果
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
sys.path.append(project_root)

def test_mask_alignment():
    """测试mask对齐效果"""
    from nature_colorblock_generate_script import (
        calculate_text_positions_in_colorblock,
        select_colorblock_erasure_regions
    )
    from render_font_proj import generate_text_lines, create_mask_image
    
    print("=" * 60)
    print("测试mask对齐修复效果")
    print("=" * 60)
    
    # 创建测试图像
    test_width, test_height = 800, 600
    test_image = Image.new('RGB', (test_width, test_height), (255, 255, 255))
    
    # 创建测试彩色块
    cb_x, cb_y = 200, 150
    cb_width, cb_height = 400, 300
    cb_color = (100, 150, 200)
    
    # 在测试图像上绘制彩色块
    draw = ImageDraw.Draw(test_image)
    draw.rectangle([cb_x, cb_y, cb_x + cb_width, cb_y + cb_height], fill=cb_color)
    
    # 尝试加载字体
    font_path = os.path.join(project_root, "resource", "font", "SimplifiedChinese")
    font = None
    
    if os.path.exists(font_path):
        for root, dirs, files in os.walk(font_path):
            for file in files:
                if file.endswith('.ttf'):
                    font_file = os.path.join(root, file)
                    try:
                        font = ImageFont.truetype(font_file, 40)
                        print(f"使用字体: {os.path.basename(font_file)}")
                        break
                    except:
                        continue
            if font:
                break
    
    if not font:
        print("未找到字体，使用默认字体")
        font = ImageFont.load_default()
    
    # 创建简单的测试文本
    test_chars = ['测', '试', '修', '复']
    lines = [{
        'chars': [
            {
                'char': char,
                'width': font.getbbox(char)[2] - font.getbbox(char)[0],
                'height': font.getbbox(char)[3] - font.getbbox(char)[1],
                'index': i
            }
            for i, char in enumerate(test_chars)
        ],
        'index': 0,
        'font': font
    }]
    
    # 计算文本位置
    positioned_lines = calculate_text_positions_in_colorblock(
        lines, cb_x, cb_y, cb_width, cb_height,
        line_spacing=20, char_spacing=10
    )
    
    if not positioned_lines:
        print("❌ 无法在彩色块内放置文字")
        return False
    
    # 在图像上渲染文字
    for line in positioned_lines:
        for char_info in line['chars']:
            char = char_info['char']
            x = char_info['x']
            y = char_info['y']
            
            # 计算实际渲染位置
            bbox = font.getbbox(char)
            render_x = x
            render_y = y + bbox[1]  # 基线位置 + 顶部偏移
            
            draw.text((render_x, render_y), char, fill=(255, 255, 255), font=font)
    
    # 生成mask区域
    colorblock_info = {
        'x': cb_x, 'y': cb_y, 'width': cb_width, 'height': cb_height,
        'color': cb_color, 'shape_mask': None
    }
    
    erasure_regions = select_colorblock_erasure_regions(
        positioned_lines, 'partial_chars', colorblock_info
    )
    
    if not erasure_regions:
        print("❌ 未生成mask区域")
        return False
    
    # 创建mask图像
    mask_image, _ = create_mask_image((test_width, test_height), erasure_regions, test_image)
    
    # 保存测试结果
    output_dir = os.path.join(script_dir, "test_output")
    os.makedirs(output_dir, exist_ok=True)
    
    test_image.save(os.path.join(output_dir, "test_with_text.png"))
    mask_image.save(os.path.join(output_dir, "test_mask.png"))
    
    # 创建对比图像
    comparison_image = Image.new('RGB', (test_width * 2, test_height), (255, 255, 255))
    comparison_image.paste(test_image, (0, 0))
    comparison_image.paste(mask_image.convert('RGB'), (test_width, 0))
    comparison_image.save(os.path.join(output_dir, "test_comparison.png"))
    
    print(f"✅ 测试结果已保存到: {output_dir}")
    print("请检查 test_comparison.png 查看mask是否正确对齐文字")
    
    return True

if __name__ == "__main__":
    success = test_mask_alignment()
    if success:
        print("\n✅ 测试完成！")
    else:
        print("\n❌ 测试失败！")
