# 数据集使用与加载说明

## 数据集目录结构概述

根据代码分析，HSYQ LaMa V2 Inpainting训练系统使用的数据集采用以下标准化目录结构：

```
数据集根目录/
├── 数据集类型1/
│   ├── gt/           # Ground Truth - 真值图像（修复后的完整图像）
│   ├── im/           # Input Image - 输入图像（待修复的原始图像）
│   ├── mask/         # Mask - 掩码图像（标记需要修复的区域）
│   ├── input/        # Input - 备用输入目录（某些场景使用）
│   ├── show/         # Show - 可视化展示图像（用于调试和展示）
│   └── image_mapping.json  # 图像映射文件（元数据信息）
├── 数据集类型2/
│   ├── gt/
│   ├── im/
│   ├── mask/
│   ├── input/
│   ├── show/
│   └── image_mapping.json
└── ...
```

## 各文件夹详细作用分析

### 1. `gt/` 文件夹 (Ground Truth)

**作用**: 存储真值图像，即经过完美修复后的目标图像

**在代码中的使用**:
- **加载位置**: `my_datasets/image_inpainting/base_dataset_v1.py` 第156行
- **路径构建**: `gt_image_path = os.path.join(self.data_root, data_name, data['gt'])`
- **图像处理**: `gt_image = Image.open(gt_image_path).convert("RGB")`
- **训练用途**: 作为生成器的监督信号，用于计算损失函数

**代码实现细节**:
```python
# base_dataset_v1.py 第161行
gt_image = Image.open(gt_image_path).convert("RGB")

# 在损失计算中作为target使用
# training_loops/image_inpainting/train_lama_inpainting.py
target = batch["gt"]  # 真值图像作为训练目标
```

### 2. `im/` 文件夹 (Input Image)

**作用**: 存储输入图像，即包含缺陷或需要修复区域的原始图像

**在代码中的使用**:
- **加载位置**: `my_datasets/image_inpainting/base_dataset_v1.py` 第154行
- **路径构建**: `im_image_path = os.path.join(self.data_root, data_name, data['im'])`
- **图像处理**: `im_image = Image.open(im_image_path).convert("RGB")`
- **训练用途**: 与mask结合生成四通道输入，送入生成器

**代码实现细节**:
```python
# base_dataset_v1.py 第159行
im_image = Image.open(im_image_path).convert("RGB")

# 在训练中与mask结合使用
# training_loops/image_inpainting/train_lama_inpainting.py
origin = batch["im"]  # 原始图像
mask = batch["mask"]  # 掩码
# 生成四通道输入: RGB + mask
noised_image = torch.cat([origin, mask], dim=1)
```

### 3. `mask/` 文件夹 (Mask)

**作用**: 存储掩码图像，标记需要修复的区域（白色区域表示需要修复）

**在代码中的使用**:
- **加载位置**: `my_datasets/image_inpainting/base_dataset_v1.py` 第155行
- **路径构建**: `mask_image_path = os.path.join(self.data_root, data_name, data['mask'])`
- **图像处理**: `mask_image = Image.open(mask_image_path).convert("L")`
- **训练用途**: 指导模型哪些区域需要修复，参与损失计算

**代码实现细节**:
```python
# base_dataset_v1.py 第160行
mask_image = Image.open(mask_image_path).convert("L")  # 转为灰度图

# 在base_dataset_v2.py中还有膨胀处理（已注释）
# mask_image = cv2.dilate(mask_image, (self.kernel_size[0],self.kernel_size[1]), iterations=1)
# mask_image = cv2.GaussianBlur(mask_image, self.kernel_size, 0.8)
```

### 4. `input/` 文件夹 (备用输入)

**作用**: 在某些特定场景下作为备用的输入图像目录

**在代码中的使用**:
- **主要出现**: `examples/image_matting/model_inference.py` 第41行
- **使用场景**: 主要用于推理阶段的图像输入
- **代码示例**: `img_root_path=r'/mnt/aicamera-mlp/hz_datasets/doc_segs_buchong/test_img/input'`

**特点**:
- 通常在推理和测试阶段使用
- 与`im/`文件夹功能类似，但用于不同的处理流程
- 在训练脚本中较少直接使用

### 5. `show/` 文件夹 (可视化展示)

**作用**: 存储用于可视化展示和调试的图像

**在代码中的使用**:
- **主要出现**: `examples/inpainting/xiaochu_tools.py` 第378-381行
- **使用场景**: 调试和结果展示
- **代码示例**:
```python
# xiaochu_tools.py 第378-381行
problem_show_img = []
ok_show_img = []
save_problem_folder_path = r'/aicamera-mlp/hz_datasets/xiaochu_dataset/test_data/uvdoc_test/visual_img_problem/'
save_ok_folder_path = r'/aicamera-mlp/hz_datasets/xiaochu_dataset/test_data/uvdoc_test/visual_img_ok/'
```

**特点**:
- 主要用于开发和调试阶段
- 存储处理过程中的中间结果
- 便于人工检查数据质量

### 6. `image_mapping.json` 文件

**作用**: 存储图像元数据和映射关系的JSON文件

**推测用途**（基于代码模式分析）:
- 可能包含图像文件名映射关系
- 存储额外的元数据信息（如图像尺寸、处理参数等）
- 用于数据集版本管理和一致性检查

**注意**: 在当前训练代码中，主要使用JSONL格式的元数据文件，而非`image_mapping.json`

## 数据加载流程详解

### 1. 元数据文件加载

**JSONL文件格式** (`train.jsonl`, `val.jsonl`, `train_crop.jsonl`, `val_crop.jsonl`):
```json
{"dataset_name": "doc_enhance_v1", "im": "image_001.jpg", "mask": "mask_001.jpg", "gt": "gt_001.jpg"}
{"dataset_name": "doc_enhance_v1", "im": "image_002.jpg", "mask": "mask_002.jpg", "gt": "gt_002.jpg"}
```

**加载代码**:
```python
# base_dataset_v1.py 第67-85行
with open(jsonl_data_path, 'r', encoding='utf-8') as file:
    for line in file:
        line = line.strip()
        if line:
            data = json.loads(line)
            self.dataset.append(data)
```

### 2. 图像路径构建

**完整路径构建逻辑**:
```python
# base_dataset_v1.py 第152-156行
data = self.dataset[idx]
data_name = data["dataset_name"]  # 从JSON获取数据集名称
im_image_path = os.path.join(self.data_root, data_name, data['im'])
mask_image_path = os.path.join(self.data_root, data_name, data['mask'])
gt_image_path = os.path.join(self.data_root, data_name, data['gt'])
```

**路径示例**:
```
数据集根目录: /aicamera-mlp/hz_datasets/xiaochu_dataset
数据集名称: doc_enhance_v1
图像文件: image_001.jpg

最终路径:
- im: /aicamera-mlp/hz_datasets/xiaochu_dataset/doc_enhance_v1/im/image_001.jpg
- mask: /aicamera-mlp/hz_datasets/xiaochu_dataset/doc_enhance_v1/mask/mask_001.jpg  
- gt: /aicamera-mlp/hz_datasets/xiaochu_dataset/doc_enhance_v1/gt/gt_001.jpg
```

### 3. 图像预处理流程

**标准预处理步骤**:
1. **图像加载**: 使用PIL加载图像并转换格式
2. **颜色增强**: 在特定数据集上应用HSV颜色增强
3. **动态处理**: 根据配置应用不同的图像处理方法
4. **张量转换**: 转换为PyTorch张量格式

**代码实现**:
```python
# base_dataset_v1.py 第159-161行
im_image = Image.open(im_image_path).convert("RGB")
mask_image = Image.open(mask_image_path).convert("L")
gt_image = Image.open(gt_image_path).convert("RGB")

# 颜色增强（特定数据集）
if self.mode == 'train' and data_name in allow_hsv_data_name and random.random() < 0.5:
    im_image, mask_image, gt_image = random_hsv_pair([im_image, mask_image, gt_image])
```

## 数据集创建和维护工具

### 1. JSONL文件生成工具

**工具位置**: `examples/inpainting/create_dataset_jsonl.py`

**功能**: 扫描数据集目录中的 `.png` 文件，自动生成训练和验证用的JSONL元数据文件

**实际核心逻辑** (第93-124行):
```python
# 获取所有图片路径 - 只扫描PNG文件
all_source_image_paths = glob.glob(f'{dataset_dir}/im/**.png')

for source_image_path in tqdm(all_source_image_paths, desc=f"Processing {dataset_name}"):
    img_name = os.path.basename(source_image_path)  # 文件名
    parts = source_image_path.split(os.sep)
    parts[-2] = 'gt'
    gt_image_path = os.path.sep.join(parts)
    parts[-2] = 'mask'
    mask_image_path = os.path.sep.join(parts)

    # 数据不完善 直接删去
    if not os.path.exists(gt_image_path) or not os.path.exists(mask_image_path):
        if not os.path.exists(str(gt_image_path)):
            print(f'数据不完善:{str(gt_image_path)}')
        if not os.path.exists(str(mask_image_path)):
            print(f'数据不完善:{str(mask_image_path)}')
        continue

    # 将绝对路径换成相对路径
    data = {
        "dataset_name": dataset_name,
        "im": str(Path(source_image_path).relative_to(dataset_dir)),
        "mask": str(Path(mask_image_path).relative_to(dataset_dir)),
        "gt": str(Path(gt_image_path).relative_to(dataset_dir)),
    }
```

**重要限制**: 当前脚本只处理 `.png` 格式的图像文件。

### 2. 数据可视化工具

**工具位置**: `examples/inpainting/visual_show.py`

**功能**: 批量可视化数据集中的图像三元组（im, mask, gt）

**使用方式**:
```python
# visual_show.py 第21-39行
def get_files_from_folder(folder):
    im_dir = os.path.join(folder, "im")
    gt_dir = os.path.join(folder, "gt")
    mask_dir = os.path.join(folder, "mask")

    im_files = [f for f in os.listdir(im_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

    all_files = []
    for filename in im_files:
        gt_path = os.path.join(gt_dir, filename)
        mask_path = os.path.join(mask_dir, filename)

        if os.path.exists(gt_path) and os.path.exists(mask_path):
            all_files.append((Path(gt_path), Path(os.path.join(im_dir, filename)), Path(mask_path)))
        else:
            print(f"[警告] 缺少对应文件: {filename}")
```

### 3. 数据清理工具

**工具位置**: `examples/inpainting/detele_folder.py`

**功能**: 批量删除指定文件名的图像文件

**使用场景**: 清理有问题的数据样本

```python
# detele_folder.py 第21-56行
target_subfolders = ['im', 'mask', 'gt']

for folder in folder_list:
    for subfolder_name in target_subfolders:
        subfolder = folder_path / subfolder_name
        if not subfolder.exists():
            continue

        for file in subfolder.iterdir():
            file_base_name = file.stem
            for base_name in base_names:
                if base_name in file_base_name:
                    os.remove(os.path.join(subfolder, file))
                    print(f"✅ 已删除: {file}")
```

## 特殊数据集处理

### 1. 裁剪数据集 (base_dataset_v2.py)

**特点**: 专门处理裁剪后的文档数据

**额外处理**:
- **裁剪索引**: 使用`crop_index`进行图像裁剪
- **膨胀处理**: 对mask进行膨胀和高斯模糊（可选）
- **缺失处理**: 当gt图像不存在时，生成全黑图像

**代码实现**:
```python
# base_dataset_v2.py 第216-226行
crop_index = data['crop_index']
im_image = im_image.crop(crop_index)
mask_image = mask_image.crop(crop_index)

if os.path.exists(gt_image_path):
    gt_image = gt_image.crop(crop_index)
else:
    width, height = mask_image.size
    gt_image = Image.new("RGB", (width, height), color=(0, 0, 0))  # 全黑图像
```

### 2. 动态分辨率处理

**功能**: 根据配置动态调整图像分辨率

**实现位置**: `dynamic_collate_fn` 函数

**处理流程**:
1. **分辨率选择**: 从`res4cleardoc`参数中随机选择分辨率
2. **处理方法选择**: 随机选择图像处理方法（resize、crop等）
3. **批处理**: 将处理后的图像组合成批次

```python
# base_dataset_v1.py dynamic_collate_fn函数
resolution = resolution_fun()  # 动态获取分辨率
selected_method = random.choices(methods, weights=weights)[0]  # 随机选择处理方法
processed_images = selected_method["func"](images, resolution)  # 应用处理
```

## 验证图像处理

### 1. 验证图像目录结构

**要求的目录结构**:
```
validation_image_dir/
├── 数据集类型1/
│   └── im/
│       ├── image1.jpg
│       ├── image2.jpg
│       └── ...
├── 数据集类型2/
│   └── im/
│       ├── image1.jpg
│       └── ...
└── ...
```

### 2. 验证图像加载流程

**代码位置**: `training_loops/image_inpainting/train_lama_inpainting.py` 第452-541行 (`log_validation`函数内)

**处理步骤**:
1. **递归扫描**: 使用 `image_utils.get_all_image_path(validation_image_dir, recursive=True)` 获取所有图像 (第460行)
2. **路径筛选**: 通过路径解析筛选，只处理 `data_type=='im'` 的图像 (第469行)
3. **推理生成**: 调用 `generator_forward` 生成预测结果 (第502-505行)
4. **后处理**: 应用 `histogram_matching` 直方图匹配和 `feathered_edge_blend` 边缘融合 (第517-522行)
5. **结果保存**: 调用 `save_grouped_images` 保存可视化结果 (第536-541行)

**实际代码实现**:
```python
# train_lama_inpainting.py 第460-476行
all_val_image_path = image_utils.get_all_image_path(validation_image_dir, recursive=True, path_op=Path)
use_all_val_image_path = []
for img_path in all_val_image_path:
    img_path = str(img_path)
    # 判断该文件属于哪种类型，img/mask/gt
    path_list = img_path.split(os.path.sep)
    data_type = path_list[-2]
    # 判断该文件属于哪种数据集，train/test/val
    dataset_type = path_list[-3]
    if data_type == 'im':  # 只处理im目录下的图像
        use_all_val_image_path.append({
            'im_path': img_path,
            'data_type': data_type,
            'dataset_type': dataset_type
        })
```

## 数据集配置最佳实践

### 1. 目录命名规范

- **数据集类型**: 使用描述性名称，如`doc_enhance_v1`, `table_uvdoc`等
- **文件命名**: 保持im、mask、gt三个文件夹中的同名文件对应关系
- **扩展名**: 支持常见图像格式（jpg, png, jpeg等）

### 2. 数据质量检查

- **完整性检查**: 确保每个样本都有对应的im、mask、gt文件
- **尺寸一致性**: 同一样本的三张图像应具有相同尺寸
- **掩码有效性**: 确保mask图像正确标记了需要修复的区域

### 3. 性能优化建议

- **数据预加载**: 使用多线程数据加载器提高I/O效率
- **缓存机制**: 对于小数据集，可以考虑将图像预加载到内存
- **格式优化**: 根据需要选择合适的图像压缩格式和质量

这种标准化的数据集结构确保了训练系统的稳定性和可扩展性，同时提供了丰富的工具支持数据集的创建、维护和验证。
