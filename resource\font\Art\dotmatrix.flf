flf2a$ 10 10 23 0 3
dotmatrix.flf by <PERSON> (<EMAIL>)
last revision - 8/21/95

      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @
      $$$$$$$$$      @@
        $ _ $        @
        $(_)$        @
        $(_)$        @
        $(_)$        @
        $(_)$        @
        $   $        @
        $ _ $        @
        $(_)$        @
        $   $        @
        $   $        @@
     $  _   _  $     @
     $ (_) (_) $     @
     $ (_) (_) $     @
     $ (_) (_) $     @
     $         $     @
     $         $     @
     $         $     @
     $         $     @
     $         $     @
     $         $     @@
  $    _     _    $  @
  $   (_)   (_)   $  @
  $ _ (_) _ (_) _ $  @
  $(_)(_)(_)(_)(_)$  @
  $ _ (_) _ (_) _ $  @
  $(_)(_)(_)(_)(_)$  @
  $   (_)   (_)   $  @
  $   (_)   (_)   $  @
  $               $  @
  $               $  @@
  $      _         $ @
  $   _ (_) _  _   $ @
  $ _(_)(_)(_)(_)  $ @
  $(_)_ (_) _  _   $ @
  $  (_)(_)(_)(_)_ $ @
  $   _ (_) _  _(_)$ @
  $  (_)(_)(_)(_)  $ @
  $     (_)        $ @
  $                $ @
  $                $ @@
  $ _  _        _ $  @
  $(_)(_)     _(_)$  @
  $(_)(_)   _(_)  $  @
  $       _(_)    $  @
  $     _(_)      $  @
  $   _(_)   _  _ $  @
  $ _(_)    (_)(_)$  @
  $(_)      (_)(_)$  @
  $               $  @
  $               $  @@
   $   _  _       $  @
   $ _(_)(_)_     $  @
   $(_)_  _(_)    $  @
   $  (_)(_)    _ $  @
   $ _ (_)_   _(_)$  @
   $(_)  (_)_(_)  $  @
   $(_)_  _(_)_   $  @
   $  (_)(_) (_)  $  @
   $              $  @
   $              $  @@
      $  _  _  $     @
      $ (_)(_) $     @
      $ (_)(_) $     @
      $   (_)  $     @
      $  (_)   $     @
      $        $     @
      $        $     @
      $        $     @
      $        $     @
      $        $     @@
      $      _ $     @
      $   _ (_)$     @
      $ _(_)   $     @
      $(_)     $     @
      $(_)     $     @
      $(_)_    $     @
      $  (_) _ $     @
      $     (_)$     @
      $        $     @
      $        $     @@
      $ _      $     @
      $(_) _   $     @
      $   (_)_ $     @
      $     (_)$     @
      $     (_)$     @
      $    _(_)$     @
      $ _ (_)  $     @
      $(_)     $     @
      $        $     @
      $        $     @@
  $               $  @
  $   _       _   $  @
  $  (_)_   _(_)  $  @
  $ _  (_)_(_)  _ $  @
  $(_)(_)(_)(_)(_)$  @
  $   _(_) (_)_   $  @
  $  (_)     (_)  $  @
  $               $  @
  $               $  @
  $               $  @@
  $               $  @
  $       _       $  @
  $      (_)      $  @
  $ _  _ (_) _  _ $  @
  $(_)(_)(_)(_)(_)$  @
  $      (_)      $  @
  $      (_)      $  @
  $               $  @
  $               $  @
  $               $  @@
       $       $     @
       $       $     @
       $       $     @
       $       $     @
       $       $     @
       $ _  _  $     @
       $(_)(_) $     @
       $(_)(_) $     @
       $  (_)  $     @
       $ (_)   $     @@
  $               $  @
  $               $  @
  $               $  @
  $ _  _  _  _  _ $  @
  $(_)(_)(_)(_)(_)$  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @@
       $       $     @
       $       $     @
       $       $     @
       $       $     @
       $       $     @
       $ _  _  $     @
       $(_)(_) $     @
       $(_)(_) $     @
       $       $     @
       $       $     @@
                _    @
              _(_)   @
            _(_)     @
          _(_)       @
        _(_)         @
      _(_)           @
    _(_)             @
   (_)               @
                     @
                     @@
         _  _        @
      _ (_)(_) _     @
     (_)      (_)    @
    (_)        (_)   @
    (_)        (_)   @
    (_)        (_)   @
     (_) _  _ (_)    @
        (_)(_)       @
                     @
                     @@
          _          @
       _ (_)         @
      (_)(_)         @
         (_)         @
         (_)         @
         (_)         @
       _ (_) _       @
      (_)(_)(_)      @
                     @
                     @@
       _  _  _       @
    _ (_)(_)(_) _    @
   (_)         (_)   @
             _ (_)   @
          _ (_)      @
       _ (_)         @
    _ (_) _  _  _    @
   (_)(_)(_)(_)(_)   @
                     @
                     @@
      _  _  _  _     @
    _(_)(_)(_)(_)_   @
   (_)          (_)  @
            _  _(_)  @
           (_)(_)_   @
    _           (_)  @
   (_)_  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @
                     @@
             _       @
          _ (_)      @
       _ (_)(_)      @
    _ (_)   (_)      @
   (_) _  _ (_) _    @
   (_)(_)(_)(_)(_)   @
            (_)      @
            (_)      @
                     @
                     @@
    _  _  _  _  _    @
   (_)(_)(_)(_)(_)   @
   (_) _  _  _       @
   (_)(_)(_)(_) _    @
               (_)   @
    _          (_)   @
   (_) _  _  _ (_)   @
      (_)(_)(_)      @
                     @
                     @@
         _  _  _     @
       _(_)(_)(_)    @
     _(_)            @
    (_) _  _  _      @
    (_)(_)(_)(_)_    @
    (_)        (_)   @
    (_)_  _  _ (_)   @
      (_)(_)(_)      @
                     @
                     @@
    _  _  _  _  _    @
   (_)(_)(_)(_)(_)   @
             _(_)    @
           _(_)      @
         _(_)        @
       _(_)          @
     _(_)            @
    (_)              @
                     @
                     @@
      _  _  _  _     @
    _(_)(_)(_)(_)_   @
   (_)          (_)  @
   (_)_  _  _  _(_)  @
    _(_)(_)(_)(_)_   @
   (_)          (_)  @
   (_)_  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @
                     @@
       _  _  _       @
    _ (_)(_)(_) _    @
   (_)         (_)   @
   (_) _  _  _ (_)   @
      (_)(_)(_)(_)   @
              _(_)   @
      _  _  _(_)     @
     (_)(_)(_)       @
                     @
                     @@
       $       $     @
       $       $     @
       $ _  _  $     @
       $(_)(_) $     @
       $(_)(_) $     @
       $ _  _  $     @
       $(_)(_) $     @
       $(_)(_) $     @
       $       $     @
       $       $     @@
       $       $     @
       $       $     @
       $ _  _  $     @
       $(_)(_) $     @
       $(_)(_) $     @
       $ _  _  $     @
       $(_)(_) $     @
       $(_)(_) $     @
       $  (_)  $     @
       $ (_)   $     @@
    $          _ $   @
    $       _ (_)$   @
    $    _ (_)   $   @
    $ _ (_)      $   @
    $(_) _       $   @
    $   (_) _    $   @
    $      (_) _ $   @
    $         (_)$   @
    $            $   @
    $            $   @@
  $               $  @
  $               $  @
  $ _  _  _  _  _ $  @
  $(_)(_)(_)(_)(_)$  @
  $ _  _  _  _  _ $  @
  $(_)(_)(_)(_)(_)$  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @@
    $ _          $   @
    $(_) _       $   @
    $   (_) _    $   @
    $      (_) _ $   @
    $       _ (_)$   @
    $    _ (_)   $   @
    $ _ (_)      $   @
    $(_)         $   @
    $            $   @
    $            $   @@
   $    _  _  _   $  @
   $ _ (_)(_)(_)_ $  @
   $(_)        (_)$  @
   $         _ (_)$  @
   $      _ (_)   $  @
   $     (_)      $  @
   $      _       $  @
   $     (_)      $  @
   $              $  @
   $              $  @@
   $    _  _  _   $  @
   $  _(_)(_)(_)_ $  @
   $ (_)  _  _ (_)$  @
   $(_)  (_)(_)(_)$  @
   $(_) (_)  _ (_)$  @
   $(_)  (_)(_)(_)$  @
   $ (_)  _  _  _ $  @
   $  (_)(_)(_)(_)$  @
   $              $  @
   $              $  @@
          _          @
        _(_)_        @
      _(_) (_)_      @
    _(_)     (_)_    @
   (_) _  _  _ (_)   @
   (_)(_)(_)(_)(_)   @
   (_)         (_)   @
   (_)         (_)   @
                     @
                     @@
    _  _  _  _       @
   (_)(_)(_)(_) _    @
    (_)        (_)   @
    (_) _  _  _(_)   @
    (_)(_)(_)(_)_    @
    (_)        (_)   @
    (_)_  _  _ (_)   @
   (_)(_)(_)(_)      @
                     @
                     @@
       _  _  _       @
    _ (_)(_)(_) _    @
   (_)         (_)   @
   (_)               @
   (_)               @
   (_)          _    @
   (_) _  _  _ (_)   @
      (_)(_)(_)      @
                     @
                     @@
    _  _  _  _       @
   (_)(_)(_)(_)      @
    (_)      (_)_    @
    (_)        (_)   @
    (_)        (_)   @
    (_)       _(_)   @
    (_)_  _  (_)     @
   (_)(_)(_)(_)      @
                     @
                     @@
    _  _  _  _  _    @
   (_)(_)(_)(_)(_)   @
   (_)               @
   (_) _  _          @
   (_)(_)(_)         @
   (_)               @
   (_) _  _  _  _    @
   (_)(_)(_)(_)(_)   @
                     @
                     @@
    _  _  _  _  _    @
   (_)(_)(_)(_)(_)   @
   (_)               @
   (_) _  _          @
   (_)(_)(_)         @
   (_)               @
   (_)               @
   (_)               @
                     @
                     @@
       _  _  _       @
    _ (_)(_)(_) _    @
   (_)         (_)   @
   (_)    _  _  _    @
   (_)   (_)(_)(_)   @
   (_)         (_)   @
   (_) _  _  _ (_)   @
      (_)(_)(_)(_)   @
                     @
                     @@
    _           _    @
   (_)         (_)   @
   (_)         (_)   @
   (_) _  _  _ (_)   @
   (_)(_)(_)(_)(_)   @
   (_)         (_)   @
   (_)         (_)   @
   (_)         (_)   @
                     @
                     @@
       _  _  _       @
      (_)(_)(_)      @
         (_)         @
         (_)         @
         (_)         @
         (_)         @
       _ (_) _       @
      (_)(_)(_)      @
                     @
                     @@
          _  _  _    @
         (_)(_)(_)   @
            (_)      @
            (_)      @
            (_)      @
     _      (_)      @
    (_)  _  (_)      @
     (_)(_)(_)       @
                     @
                     @@
    _           _    @
   (_)       _ (_)   @
   (_)    _ (_)      @
   (_) _ (_)         @
   (_)(_) _          @
   (_)   (_) _       @
   (_)      (_) _    @
   (_)         (_)   @
                     @
                     @@
    _                @
   (_)               @
   (_)               @
   (_)               @
   (_)               @
   (_)               @
   (_) _  _  _  _    @
   (_)(_)(_)(_)(_)   @
                     @
                     @@
    _           _    @
   (_) _     _ (_)   @
   (_)(_)   (_)(_)   @
   (_) (_)_(_) (_)   @
   (_)   (_)   (_)   @
   (_)         (_)   @
   (_)         (_)   @
   (_)         (_)   @
                     @
                     @@
    _           _    @
   (_) _       (_)   @
   (_)(_)_     (_)   @
   (_)  (_)_   (_)   @
   (_)    (_)_ (_)   @
   (_)      (_)(_)   @
   (_)         (_)   @
   (_)         (_)   @
                     @
                     @@
      _  _  _  _     @
    _(_)(_)(_)(_)_   @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)_  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @
                     @@
     _  _  _  _      @
    (_)(_)(_)(_)_    @
    (_)        (_)   @
    (_) _  _  _(_)   @
    (_)(_)(_)(_)     @
    (_)              @
    (_)              @
    (_)              @
                     @
                     @@
      _  _  _  _     @
    _(_)(_)(_)(_)_   @
   (_)          (_)  @
   (_)          (_)  @
   (_)     _    (_)  @
   (_)    (_) _ (_)  @
   (_)_  _  _(_) _   @
     (_)(_)(_)  (_)  @
                     @
                     @@
    _  _  _  _       @
   (_)(_)(_)(_) _    @
   (_)         (_)   @
   (_) _  _  _ (_)   @
   (_)(_)(_)(_)      @
   (_)   (_) _       @
   (_)      (_) _    @
   (_)         (_)   @
                     @
                     @@
      _  _  _  _     @
    _(_)(_)(_)(_)_   @
   (_)          (_)  @
   (_)_  _  _  _     @
     (_)(_)(_)(_)_   @
    _           (_)  @
   (_)_  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @
                     @@
    _  _  _  _  _    @
   (_)(_)(_)(_)(_)   @
         (_)         @
         (_)         @
         (_)         @
         (_)         @
         (_)         @
         (_)         @
                     @
                     @@
    _            _   @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)_  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @
                     @@
    _           _    @
   (_)         (_)   @
   (_)         (_)   @
   (_)_       _(_)   @
     (_)     (_)     @
      (_)   (_)      @
       (_)_(_)       @
         (_)         @
                     @
                     @@
   _             _   @
  (_)           (_)  @
  (_)           (_)  @
  (_)     _     (_)  @
  (_)   _(_)_   (_)  @
  (_)  (_) (_)  (_)  @
  (_)_(_)   (_)_(_)  @
    (_)       (_)    @
                     @
                     @@
    _           _    @
   (_)_       _(_)   @
     (_)_   _(_)     @
       (_)_(_)       @
        _(_)_        @
      _(_) (_)_      @
    _(_)     (_)_    @
   (_)         (_)   @
                     @
                     @@
    _           _    @
   (_)_       _(_)   @
     (_)_   _(_)     @
       (_)_(_)       @
         (_)         @
         (_)         @
         (_)         @
         (_)         @
                     @
                     @@
    _  _  _  _  _    @
   (_)(_)(_)(_)(_)   @
             _(_)    @
           _(_)      @
         _(_)        @
       _(_)          @
    _ (_) _  _  _    @
   (_)(_)(_)(_)(_)   @
                     @
                     @@
     $ _  _  _ $     @
     $(_)(_)(_)$     @
     $(_)      $     @
     $(_)      $     @
     $(_)      $     @
     $(_)      $     @
     $(_) _  _ $     @
     $(_)(_)(_)$     @
     $         $     @
     $         $     @@
    _                @
   (_)_              @
     (_)_            @
       (_)_          @
         (_)_        @
           (_)_      @
             (_)_    @
               (_)   @
                     @
                     @@
     $ _  _  _ $     @
     $(_)(_)(_)$     @
     $      (_)$     @
     $      (_)$     @
     $      (_)$     @
     $      (_)$     @
     $ _  _ (_)$     @
     $(_)(_)(_)$     @
     $         $     @
     $         $     @@
  $       _       $  @
  $    _ (_) _    $  @
  $ _ (_)   (_) _ $  @
  $(_)         (_)$  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @@
 $                 $ @
 $                 $ @
 $                 $ @
 $                 $ @
 $                 $ @
 $                 $ @
 $                 $ @
 $                 $ @
 _  _  _  _  _  _  _ @
(_)(_)(_)(_)(_)(_)(_)@@
    $  _  _  $       @
    $ (_)(_) $       @
    $ (_)(_) $       @
    $  (_)   $       @
    $   (_)  $       @
    $        $       @
    $        $       @
    $        $       @
    $        $       @
    $        $       @@
                     @
                     @
      _  _  _        @
     (_)(_)(_) _     @
      _  _  _ (_)    @
    _(_)(_)(_)(_)    @
   (_)_  _  _ (_)_   @
     (_)(_)(_)  (_)  @
                     @
                     @@
     _               @
    (_)              @
    (_) _  _  _      @
    (_)(_)(_)(_)_    @
    (_)        (_)   @
    (_)        (_)   @
    (_) _  _  _(_)   @
    (_)(_)(_)(_)     @
                     @
                     @@
                     @
                     @
       _  _  _       @
     _(_)(_)(_)      @
    (_)              @
    (_)              @
    (_)_  _  _       @
      (_)(_)(_)      @
                     @
                     @@
                _    @
               (_)   @
       _  _  _ (_)   @
     _(_)(_)(_)(_)   @
    (_)        (_)   @
    (_)        (_)   @
    (_)_  _  _ (_)   @
      (_)(_)(_)(_)   @
                     @
                     @@
                     @
                     @
     _  _  _  _      @
    (_)(_)(_)(_)_    @
   (_) _  _  _ (_)   @
   (_)(_)(_)(_)(_)   @
   (_)_  _  _  _     @
     (_)(_)(_)(_)    @
                     @
                     @@
           _  _      @
         _(_)(_)     @
      _ (_) _        @
     (_)(_)(_)       @
        (_)          @
        (_)          @
        (_)          @
        (_)          @
                     @
                     @@
                     @
                     @
       _  _  _  _    @
     _(_)(_)(_)(_)   @
    (_)        (_)   @
    (_)        (_)   @
    (_)_  _  _ (_)   @
      (_)(_)(_)(_)   @
       _  _  _ (_)   @
      (_)(_)(_)      @@
     _               @
    (_)              @
    (_) _  _  _      @
    (_)(_)(_)(_)_    @
    (_)        (_)   @
    (_)        (_)   @
    (_)        (_)   @
    (_)        (_)   @
                     @
                     @@
          _          @
         (_)         @
       _  _          @
      (_)(_)         @
         (_)         @
         (_)         @
       _ (_) _       @
      (_)(_)(_)      @
                     @
                     @@
              _      @
             (_)     @
           _  _      @
          (_)(_)     @
             (_)     @
             (_)     @
             (_)     @
     _      _(_)     @
    (_)_  _(_)       @
      (_)(_)         @@
     _               @
    (_)              @
    (_)     _        @
    (_)   _(_)       @
    (_) _(_)         @
    (_)(_)_          @
    (_)  (_)_        @
    (_)    (_)       @
                     @
                     @@
       _  _          @
      (_)(_)         @
         (_)         @
         (_)         @
         (_)         @
         (_)         @
       _ (_) _       @
      (_)(_)(_)      @
                     @
                     @@
                     @
                     @
     _  _   _  _     @
    (_)(_)_(_)(_)    @
   (_)   (_)   (_)   @
   (_)   (_)   (_)   @
   (_)   (_)   (_)   @
   (_)   (_)   (_)   @
                     @
                     @@
                     @
                     @
     _  _  _  _      @
    (_)(_)(_)(_)_    @
    (_)        (_)   @
    (_)        (_)   @
    (_)        (_)   @
    (_)        (_)   @
                     @
                     @@
                     @
                     @
       _  _  _       @
    _ (_)(_)(_) _    @
   (_)         (_)   @
   (_)         (_)   @
   (_) _  _  _ (_)   @
      (_)(_)(_)      @
                     @
                     @@
                     @
                     @
    _  _  _  _       @
   (_)(_)(_)(_)_     @
   (_)        (_)    @
   (_)        (_)    @
   (_) _  _  _(_)    @
   (_)(_)(_)(_)      @
   (_)               @
   (_)               @@
                     @
                     @
      _  _  _  _     @
    _(_)(_)(_)(_)    @
   (_)        (_)    @
   (_)        (_)    @
   (_)_  _  _ (_)    @
     (_)(_)(_)(_)    @
              (_)    @
              (_)    @@
                     @
                     @
    _       _  _     @
   (_)_  _ (_)(_)    @
     (_)(_)          @
     (_)             @
     (_)             @
     (_)             @
                     @
                     @@
                     @
                     @
      _  _  _  _     @
    _(_)(_)(_)(_)    @
   (_)_  _  _  _     @
     (_)(_)(_)(_)_   @
      _  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @
                     @@
        _            @
       (_)           @
     _ (_) _  _      @
    (_)(_)(_)(_)     @
       (_)           @
       (_)     _     @
       (_)_  _(_)    @
         (_)(_)      @
                     @
                     @@
                     @
                     @
    _         _      @
   (_)       (_)     @
   (_)       (_)     @
   (_)       (_)     @
   (_)_  _  _(_)_    @
     (_)(_)(_) (_)   @
                     @
                     @@
                     @
                     @
  _               _  @
 (_)_           _(_) @
   (_)_       _(_)   @
     (_)_   _(_)     @
       (_)_(_)       @
         (_)         @
                     @
                     @@
                     @
                     @
   _             _   @
  (_)           (_)  @
  (_)     _     (_)  @
  (_)_  _(_)_  _(_)  @
    (_)(_) (_)(_)    @
      (_)   (_)      @
                     @
                     @@
                     @
                     @
     _         _     @
    (_) _   _ (_)    @
       (_)_(_)       @
        _(_)_        @
     _ (_) (_) _     @
    (_)       (_)    @
                     @
                     @@
                     @
                     @
  _               _  @
 (_)_           _(_) @
   (_)_       _(_)   @
     (_)_   _(_)     @
       (_)_(_)       @
        _(_)         @
   _  _(_)           @
  (_)(_)             @@
                     @
                     @
      _  _  _  _     @
     (_)(_)(_)(_)    @
           _ (_)     @
        _ (_)        @
      _(_)  _  _     @
     (_)(_)(_)(_)    @
                     @
                     @@
    $      _  _ $    @
    $    _(_)(_)$    @
    $   (_)     $    @
    $ _ (_)     $    @
    $(_) _      $    @
    $   (_)     $    @
    $   (_)_  _ $    @
    $     (_)(_)$    @
    $           $    @
    $           $    @@
       $  _  $       @
       $ (_) $       @
       $ (_) $       @
       $ (_) $       @
       $  _  $       @
       $ (_) $       @
       $ (_) $       @
       $ (_) $       @
       $     $       @
       $     $       @@
    $ _  _      $    @
    $(_)(_)_    $    @
    $     (_)   $    @
    $     (_) _ $    @
    $      _ (_)$    @
    $     (_)   $    @
    $ _  _(_)   $    @
    $(_)(_)     $    @
    $           $    @
    $           $    @@
  $               $  @
  $   _  _      _ $  @
  $ _(_)(_)_  _(_)$  @
  $(_)    (_)(_)  $  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @
  $               $  @@
       _     _       @
      (_) _ (_)      @
        _(_)_        @
      _(_) (_)_      @
    _(_)     (_)_    @
   (_) _  _  _ (_)   @
   (_)(_)(_)(_)(_)   @
   (_)         (_)   @
   (_)         (_)   @
                     @@
       _     _       @
      (_)   (_)      @
      _  _  _  _     @
    _(_)(_)(_)(_)_   @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)_  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @@
        _     _      @
    _  (_)   (_) _   @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)          (_)  @
   (_)_  _  _  _(_)  @
     (_)(_)(_)(_)    @
                     @@
        _     _      @
       (_)   (_)     @
      _  _  _        @
     (_)(_)(_) _     @
      _  _  _ (_)    @
    _(_)(_)(_)(_)    @
   (_)      _ (_)_   @
     (_)(_)(_)  (_)  @
                     @
                     @@
       _     _       @
      (_)   (_)      @
       _  _  _       @
    _ (_)(_)(_) _    @
   (_)         (_)   @
   (_)         (_)   @
   (_) _  _  _ (_)   @
      (_)(_)(_)      @
                     @
                     @@
      _     _        @
     (_)   (_)       @
    _         _      @
   (_)       (_)     @
   (_)       (_)     @
   (_)       (_)     @
   (_)_  _  _(_)_    @
     (_)(_)(_) (_)   @
                     @
                     @@
@
@
@
@
@
@
@
@
@
@@
