tlf2a 3 3 8 -1 6 0 0 0
╓─────[ rustofat ]─[ v1.0.0 ]  
║ an ascii art font by xero (http://xero.nu)
║ props to rusty by roy<PERSON>c (http://roysac.com)
║ and future by sam hocevar (<EMAIL>)
╙─────────────────────────────────────────── ─ ─ 
  
 @
 @
 @@
┓!
┃!
o!!
┓┓"
  "
  "
╋╋@
╋╋@
  @@
┏╋┓$
┏╋┛$
┗╋┛$$
o ┏%
┏━┛%
┛ o%%
┏┓ &
┃━╋&
┇━┛&&
┓'
 '
 ''
 ┏ (
 ┃ (
 ┗ ((
 ┓ )
 ┃ )
 ┛ ))
╻ ╻*
╺╋╸*
╹ ╹**
 ╻ +
╺╋╸+
 ╹ ++
 ,
 ,
┛,,
  -
━━-
  --
 .
 .
o..
 ╻/
┏┛/
╹ //
┏━┓0
┃/┃0
┛━┛00
┫1
┃1
┇11
┏━┓2
┏━┛2
┗━━22
┏━┓3
 ━┫3
┗━┛33
┓ ┓4
┗━┫4
  ┇44
┏━━5
┗━┓5
┗━┛55
┏━━6
┣━┓6
┗━┛66
━━┓7
 ┏┛7
 ┇ 77
┏━┓8
┣━┫8
┗━┛88
┏━┓9
┗━┫9
┗━┛99
 :
o: 
o::
 ;
o;
┛;;
 @
 @
<@@
  =
━━=
━━==
 @
 @
>@@
┏━┓?
 ┏┛?
 o ??
┏━┓@
┃┣┛@
┗━ @@
┳━┓A
┃━┫A
┛ ┇AA
┳━┓B
┃━┃B
┇━┛BB
┏━┓C
┃  C
┗━┛CC
┳━┓D
┃ ┃D
┇━┛DD
┳━┓E
┣━ E
┻━┛EE
┳━┓F
┣━ F
┇  FF
┏━┓G
┃ ┳G
┇━┛GG
┳ ┳H
┃━┫H
┇ ┻HH
oI
┃I
┇II
  ┳J
┏ ┃J
┗━┇JJ
┳┏ K
┣┻┓K
┇ ┛KK
┳  L
┃  L
┇━┛LL
┏┏┓M
┃┃┃M
┛ ┇MM
┏┓┓N
┃┃┃N
┇┗┛NN
┏━┓O
┃ ┃O
┛━┛OO
┳━┓P
┃━┛P
┇  PP
┓━┓Q
┃ ┃Q
┗━\QQ
┳━┓R
┃┳┛R
┇┗┛RR
┓━┓S
┗━┓S
━━┛SS
┏┓┓T
 ┃ T
 ┇ TT
┳ ┓U
┃ ┃U
┇━┛UU
┓ ┳V
┃┏┛V
┗┛ VV
┓ ┳W
┃┃┃W
┗┻┇WW
┓ ┃X
┏╋┛X
┇ ┗XX
┓ ┳Y
┗┏┛Y
 ┇ YY
┏━┓Z
┏━┛Z
┗━┛ZZ
 ┏ [
 ┃ [
 ┗ [[
╻ \ 
┗┓\
 ╹\\
 ┓ ]
 ┃ ]
 ┛ ]]
┏┻┓@
   @
   @@
   _
   _
━━━__
┓`
 `
 ``
┳━┓a
┃━┫a
┛ ┇aa
┳━┓b
┃━┃b
┇━┛bb
┏━┓c
┃  c
┗━┛cc
┳━┓d
┃ ┃d
┇━┛dd
┳━┓e
┣━ e
┻━┛ee
┳━┓f
┣━ f
┇  ff
┏━┓g
┃ ┳g
┇━┛gg
┳ ┳h
┃━┫h
┇ ┻hh
oi
┃i 
┇ii
  ┳j
┏ ┃j
┗━┇jj
┳┏ k
┣┻┓k
┇ ┛kk
┳  l
┃  l
┇━┛ll
┏┏┓m
┃┃┃m 
┛ ┇mm
┏┓┓n
┃┃┃n
┇┗┛nn
┏━┓o
┃ ┃o
┛━┛oo
┳━┓p
┃━┛p
┇  pp
┓━┓q
┃ ┃q
┗━\qq
┳━┓r
┃┳┛r
┇┗┛rr
┓━┓s
┗━┓s
━━┛ss
┏┓┓t
 ┃ t
 ┇ tt
┳ ┓u
┃ ┃u
┇━┛uu
┓ ┳v
┃┏┛v
┗┛ vv
┓ ┳w
┃┃┃w
┗┻┇ww
┓ ┃x
┏╋┛x
┇ ┗xx
┓ ┳y
┗┏┛y
 ┇ yy
┏━┓z
┏━┛z
┗━┛zz
 ┏ {
 ┫ {
 ┗ {{  
┃|
┃|
┇||
 ┓ }
 ┣ }
 ┛ }}
   ~
┏━┛~
   ~~
┳━┓┳━┓Ä
┃━┫┣━ Ä
┛ ┇┻━┛ÄÄ
┏━┓┳━┓Ö
┃ ┃┣━ Ö
┛━┛┻━┛ÖÖ
┳ ┓┳━┓Ü
┃ ┃┣━ Ü
┇━┛┻━┛ÜÜ
┳━┓┳━┓ä
┃━┫┣━ ä
┛ ┇┻━┛ää
┏━┓┳━┓ö
┃ ┃┣━ ö
┛━┛┻━┛öö
┳ ┓┳━┓ü
┃ ┃┣━ ü
┇━┛┻━┛üü
┓━┓┓━┓ß
┗━┓┗━┓ß
━━┛━━┛ßß
0x2500 ━ BOX DRAWINGS LIGHT HORIZONTAL
   @
━━━@
   @@
0x2501 ━ BOX DRAWINGS HEAVY HORIZONTAL
   @
━━━@
   @@
0x2502 ┃ BOX DRAWINGS LIGHT VERTICAL
 ┃ @
 ┃ @
 ┃@@
0x2503 ┃ BOX DRAWINGS HEAVY VERTICAL
 ┃ @
 ┃ @
 ┃@@
0x250C ┏ BOX DRAWINGS LIGHT DOWN AND RIGHT
   @
 ┏━@
 ┃ @@
0x250F ┏ BOX DRAWINGS HEAVY DOWN AND RIGHT
   @
 ┏━@
 ┃ @@
0x2510 ┓ BOX DRAWINGS LIGHT DOWN AND LEFT
   @
━┓ @
 ┃@@
0x2513 ┓ BOX DRAWINGS HEAVY DOWN AND LEFT
   @
━┓ @
 ┃@@
0x2514 ┗ BOX DRAWINGS LIGHT UP AND RIGHT
 ┃ @
 ┗━@
   @@
0x2517 ┗ BOX DRAWINGS HEAVY UP AND RIGHT
 ┃ @
 ┗━@
   @@
0x2518 ┛ BOX DRAWINGS LIGHT UP AND LEFT
 ┃ @
━┛ @
  @@
0x251B ┛ BOX DRAWINGS HEAVY UP AND LEFT
 ┃ @
━┛ @
  @@
0x251c ┣ BOX DRAWINGS LIGHT VERTICAL AND RIGHT
 ┃ @
 ┣━@
 ┃ @@
0x2523 ┣ BOX DRAWINGS HEAVY VERTICAL AND RIGHT
 ┃ @
 ┣━@
 ┃ @@
0x2524 ┫ BOX DRAWINGS LIGHT VERTICAL AND LEFT
 ┃ @
━┫ @
 ┃@@
0x252B ┫ BOX DRAWINGS HEAVY VERTICAL AND LEFT
 ┃ @
━┫ @
 ┃@@
0x252c ┳ BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
   @
━┳━@
 ┃ @@
0x2533 ┳ BOX DRAWINGS HEAVY DOWN AND HORIZONTAL
   @
━┳━@
 ┃ @@
0x2534 ┻ BOX DRAWINGS LIGHT UP AND HORIZONTAL
 ┃ @
━┻━@
   @@
0x253B ┻ BOX DRAWINGS HEAVY UP AND HORIZONTAL
 ┃ @
━┻━@
   @@
0x253c ╋ BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
 ┃ @
━╋━@
 ┃ @@
0x254B ╋ BOX DRAWINGS HEAVY VERTICAL AND HORIZONTAL
 ┃ @
━╋━@
 ┃ @@
0x2578 ╸ BOX DRAWINGS HEAVY LEFT
   @
━━ @
  @@
0x2579 ╹ BOX DRAWINGS HEAVY UP
 ┃ @
 ╹ @
  @@
0x257A ╺ BOX DRAWINGS HEAVY RIGHT
   @
 ━━@
   @@
0x257B ╻ BOX DRAWINGS HEAVY DOWN
   @
 ╻ @
 ┃@@
0x2580 ▀ UPPER HALF BLOCK
┃┃┃@
╹╹╹@
   @@
0x2584 ▄ LOWER HALF BLOCK
   @
╻╻╻@
┃┃┃@
0x2588 █ FULL BLOCK
┃┃┃@
┃┃┃@
┃┃┃@@
0x25A0 ■ BLACK SQUARE
╻╻╻@
┃┃┃@
╹╹╹@@
