#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/12/2 14:14
# <AUTHOR> <EMAIL>
# @FileName: create_dataset_jsonl
import glob
import os
import random
import json
from pathlib import Path
from tqdm import tqdm  # 引入 tqdm
import multiprocessing as mp
from modules.utils.image_utils import get_all_image_path_v2
# 改成多进程版本
# 数据集根目录
dataset_root = "/aicamera-mlp/hz_datasets/xiaochu_dataset"

# 数据集配置
## Release
product_mode=True # 是否是生产环境
print(f'是生产环境数据集还是debug环境数据集：{product_mode}')


if product_mode: # 生成环境数据集
    dataset_names = [
        # (数据名，随机采样数量，-1则默认全部)
        ("doc_ch_en1_filter", -1), # pdf 纯净版
        ("doc_ch_en1_filter2", -1), # pdf 纯净版

        ("doc_ch_en1_filter_enhance", 500), # pdf 降质版 -1
        ("doc_ch_en1_filter2_enhance", 500), # pdf 降质版-1

        ("doc_ch_en1_filter_uvdoc", 500), # pdf 扭曲版-1
        ("doc_ch_en1_filter2_uvdoc", 500), # pdf 扭曲版-1


        ("table", 5000),#表格 纯净版
        ("table_enhance", 500),#表格 降质版 default 5000
        ("table_uvdoc", 500),# 表格 扭曲版 dafault 5000

        ("nature_dataset", -1),# 自然场景 纯净版
        ("nature_dataset_enhance", 500),# 自然场景 降质版 default -1


    ]
else:# 测试环境数据集
    dataset_names = [
        # (数据名，随机采样数量，-1则默认全部)
        ("doc_ch_en1_filter", 10),  # pdf 纯净版
        ("doc_ch_en1_filter2", 10),  # pdf 纯净版
        ("doc_ch_en1_filter_enhance", 10),  # pdf 降质版
        ("doc_ch_en1_filter2_enhance", 10),  # pdf 降质版
        ("doc_ch_en1_filter2_uvdoc", 10),  # pdf 扭曲版

        ("table", 10),  # 表格 纯净版
        ("table_enhance", 10),  # 表格 降质版
        ("table_uvdoc", 10),  # 表格 扭曲版

        ("nature_dataset", 10),  # 自然场景 纯净版
        ("nature_dataset_enhance", 10),  # 自然场景 降质版

    ]



# 数据划分比例
train_ratio = 0.95  # 每个数据名划分95%作为训练集
val_ratio = 0.05  # 每个数据名划分5%作为验证集

if product_mode:
    # 输出文件路径
    train_output_path = os.path.join(dataset_root, "train.jsonl")
    # assert not os.path.exists(train_output_path)
    val_output_path = os.path.join(dataset_root, "val.jsonl")
    # assert not os.path.exists(val_output_path)
else:
    # 输出文件路径
    train_output_path = os.path.join(dataset_root, "train_debug.jsonl")
    # assert not os.path.exists(train_output_path)
    val_output_path = os.path.join(dataset_root, "val_debug.jsonl")
    # assert not os.path.exists(val_output_path)

# 初始化全局训练集和验证集列表
global_train_data = []
global_val_data = []
def single_dataset(dataset_root,dataset_name,train_ratio,select_num):
    dataset_dir = os.path.join(dataset_root, dataset_name)  # 在根目录下有数据集名
    if dataset_name == 'nature_dataset':
        print(dataset_name)
    # 获取所有图片路径
    print(f"进程:{os.getpid()} [Info] 正在收集数据集 {dataset_name} 的图片路径...")
    all_source_image_paths = glob.glob(f'{dataset_dir}/im/**.png')
    # 如果没有图片路径，跳过当前数据集
    if len(all_source_image_paths) == 0:
        print(f"[Warning] 数据集 {dataset_name} 下未找到任何图片路径！")
        return
    # 构建所有数据的字典
    print(f"[Info] 正在构建 {dataset_name} 的数据字典...")
    all_data = []
    for source_image_path in tqdm(all_source_image_paths, desc=f"Processing {dataset_name}"):
        img_name = os.path.basename(source_image_path)  # 文件名
        parts = source_image_path.split(os.sep)
        parts[-2] = 'gt'
        gt_image_path = os.path.sep.join(parts)
        parts[-2] = 'mask'
        mask_image_path = os.path.sep.join(parts)
        # gt_image_path = Path(str(source_image_path).replace("im", "gt"))
        # mask_image_path = Path(str(source_image_path).replace("im", "mask"))
        # 数据不完善 直接删去
        if not os.path.exists(gt_image_path) or not os.path.exists(mask_image_path):
            if not os.path.exists(str(gt_image_path)):
                print(f'数据不完善:{str(gt_image_path)}')
            if not os.path.exists(str(mask_image_path)):
                print(f'数据不完善:{str(mask_image_path)}')
            continue
        # 将绝对路径换成相对路径
        data = {
            "dataset_name": dataset_name,
            "im": str(Path(source_image_path).relative_to(dataset_dir)),
            "mask": str(Path(mask_image_path).relative_to(dataset_dir)),
            "gt": str(Path(gt_image_path).relative_to(dataset_dir)),
        }
        all_data.append(data)
    assert len(all_data) > 0, f'数据{dataset_name}加载有问题..'
    # 数据随机打乱
    random.shuffle(all_data)

    # 如果采样数量为-1，使用全部数据
    if select_num == -1 or select_num >= len(all_data):
        sampled_data = all_data
        # 如果需要重采样扩充
        if select_num > len(all_data):
            sampled_data = all_data * (select_num // len(all_data)) + random.sample(all_data, select_num % len(all_data))
    else:
        # 随机采样指定数量
        sampled_data = random.sample(all_data, select_num)

    # 数据再次随机打乱（确保采样后顺序无关）
    random.shuffle(sampled_data)

    # 按比例划分训练集和验证集
    train_size = int(len(sampled_data) * train_ratio)
    train_data = sampled_data[:train_size]
    val_data = sampled_data[train_size:]

    # 将当前数据集的训练集和验证集数据添加到全局列表
    # global_train_data.extend(train_data)
    # global_val_data.extend(val_data)

    print(f"[Info] 数据集 {dataset_name} 完成处理：")
    print(f"  - 总数据量：{len(all_data)}")
    print(f"  - 采样数据量：{len(sampled_data)}")
    print(f"  - 训练集数量：{len(train_data)}")
    print(f"  - 验证集数量：{len(val_data)}")
    return (train_data,val_data)


with mp.Pool(processes=14) as pool:
    result=[pool.apply_async(single_dataset,(dataset_root,dataset_name[0],train_ratio,dataset_name[1])) for dataset_name in dataset_names]
    print('====开始收集多进程的结果========')
    for r in result:
        train_list=r.get()[0]
        val_list=r.get()[1]
        global_train_data.extend(train_list)
        global_val_data.extend(val_list)
# 全局数据打乱（训练数据和验证数据分别打乱）
random.shuffle(global_train_data)
random.shuffle(global_val_data)
print(f'训练集数量:{len(global_train_data)}')
print(f'验证集数量:{len(global_val_data)}')

# 一次性写入训练集文件
# with open(train_output_path, 'w', encoding='utf-8') as train_file:
#     for item in tqdm(global_train_data, desc="Writing train.jsonl"):
#         train_file.write(json.dumps(item, ensure_ascii=False) + '\n')
#
# # 一次性写入验证集文件
# with open(val_output_path, 'w', encoding='utf-8') as val_file:
#     for item in tqdm(global_val_data, desc="Writing val.jsonl"):
#         val_file.write(json.dumps(item, ensure_ascii=False) + '\n')
#
print(f"[Info] 所有数据集处理完成，文件已生成：模式:{'正式' if product_mode else 'debug'}")
print(f"  - 训练集：{train_output_path}")
print(f"  - 验证集：{val_output_path}")
