##!/bin/bash
#
## 程序调度环境设置
#export PYTHONPATH=.:src
#
## 初始化accelerate配置，以及安装必要依赖
## 建议不用
##  --use_ema \

#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.:src

# 初始化accelerate配置，以及安装必要依赖
# 建议不用
#  --use_ema \
#  --ema_decay 0.999 \
#  --ema_start_step 50000 \
#  --ema_period 5 \
#pip install scipy==1.11.1
#  --clear_doc_dataset_dir /aicamera-mlp/aicamera-mlp/hz_datasets/xiaochu_dataset \
#  --res4cleardoc 768 896 1024 \
# jsonal_dataset_dir 数据集地址
export ACC_CFG=hsyq_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}
# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space
# --train_batch_size 16 一张卡2
 # res4cleardoc 768 896 1024
 #--gen_lr 8e-4

# 任务启动入口
# python  training_loops/image_inpainting/train_lama_inpainting.py \
#   --validation_image_dir /aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_clear_json/model_test_imgs_crop/\
#   --inpainting_doc_crop_dataset_dir /aicamera-mlp/aicamera-mlp/hz_datasets/xiaochu_dataset \
#   --jsonal_dataset_dir /aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_clear_json \
#   --output_dir /aicamera-mlp/ouyangyu/oyy_datasets/firsttest \
#   --res4cleardoc 512    \
#   --num_train_epochs 20 \
#   --train_batch_size 8 \
#   --dataloader_num_workers 16 \
#   --gen_lr 8e-4 \
#   --disc_lr 8e-5 \
#   --save_steps 30 \
#   --num_ckpt_to_keep 20 \
#   --mixed_precision no \
#   --lr_num_cycles 1 \
#   --lr_scheduler cosine_with_restarts \
#   --loss_cfg_yaml configs/image_inpainting/lama_denoise/losses.yaml \
#   --generator_cfg_yaml configs/image_inpainting/lama_denoise/generator.yaml \
#   --discriminator_cfg_yaml configs/image_inpainting/lama_denoise/discriminator.yaml \
#   --reuse_checkpoint /aicamera-mlp/xelawk_train_space/aicache/torch/lama_pretrained \
#   --use_adam \
#   --adam_weight_decay 0 \
#   --use_apex_sync_bn false \


python  training_loops/image_inpainting/train_lama_inpainting.py \
  --validation_image_dir /aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_clear_json/model_test_imgs_crop/\
  --inpainting_doc_crop_dataset_dir /aicamera-mlp/aicamera-mlp/hz_datasets/xiaochu_dataset \
  --jsonal_dataset_dir /aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_clear_json \
  --output_dir /aicamera-mlp/ouyangyu/oyy_datasets/firsttest \
  --res4cleardoc 512    \
  --num_train_epochs 20 \
  --train_batch_size 8 \
  --dataloader_num_workers 16 \
  --gen_lr 8e-4 \
  --disc_lr 8e-5 \
  --save_steps 30 \
  --num_ckpt_to_keep 20 \
  --mixed_precision no \
  --lr_num_cycles 1 \
  --lr_scheduler cosine_with_restarts \
  --loss_cfg_yaml configs/image_inpainting/lama_denoise/losses.yaml \
  --generator_cfg_yaml configs/image_inpainting/lama_denoise/generator.yaml \
  --discriminator_cfg_yaml configs/image_inpainting/lama_denoise/discriminator.yaml \
  --reuse_checkpoint /aicamera-mlp/xelawk_train_space/aicache/torch/lama_pretrained \
  --use_adam \
  --adam_weight_decay 0 \
  --use_apex_sync_bn false \













##  --ema_decay 0.999 \
##  --ema_start_step 50000 \
##  --ema_period 5 \
##pip install scipy==1.11.1
#export ACC_CFG=hsyq_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
#python train_accelerate_config_fmt.py && cat ${ACC_CFG}
## 第三方框架缓存环境设置
#export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
#export HF_ENDPOINT=https://hf-mirror.com
#export HF_HOME=${AICACHE_DIR}/huggingface
#export TORCH_HOME=${AICACHE_DIR}/torch
#export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
#export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space
## --inpainting_doc_crop_dataset_dir /aicamera-mlp/aicamera-mlp/hz_datasets/xiaochu_dataset \
## --clear_doc_dataset_dir /aicamera-mlp/aicamera-mlp/hz_datasets/xiaochu_dataset \
##  --validation_image_dir /aicamera-mlp/hz_datasets/xiaochu_dataset/model_test_imgs \
## 任务启动入口
#python training_loops/image_inpainting/train_lama_inpainting.py \
#  --validation_image_dir /aicamera-mlp/hz_datasets/xiaochu_dataset/model_test_imgs_crop \
#  --inpainting_doc_crop_dataset_dir /aicamera-mlp/aicamera-mlp/hz_datasets/xiaochu_dataset  \
#  --output_dir /aicamera-mlp/hz_datasets/xiaochu_train_model/train_20250515_test \
#  --res4cleardoc 768 896 1024 \
#  --num_train_epochs 10 \
#  --train_batch_size 1 \
#  --dataloader_num_workers 32 \
#  --gen_lr 8e-4 \
#  --disc_lr 8e-5 \
#  --save_steps 1 \
#  --num_ckpt_to_keep 20 \
#  --mixed_precision no \
#  --lr_num_cycles 1 \
#  --lr_scheduler cosine_with_restarts \
#  --loss_cfg_yaml configs/image_denoising/lama_denoise/losses.yaml \
#  --generator_cfg_yaml configs/image_denoising/lama_denoise/generator.yaml \
#  --discriminator_cfg_yaml configs/image_denoising/lama_denoise/discriminator.yaml \
#  --reuse_checkpoint /aicamera-mlp/xelawk_train_space/aicache/torch/lama_pretrained \
#  --use_adam \
#  --adam_weight_decay 0 \
#  --debug