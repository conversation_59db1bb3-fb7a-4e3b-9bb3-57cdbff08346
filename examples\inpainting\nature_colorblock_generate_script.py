#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自然场景+彩色块文字消除数据集生成脚本

技术要点和设计规格：

1. 彩色块生成方式：
   - 形状分布：60%圆角矩形，30%椭圆/圆形，10%随机多边形/斑块
   - 颜色：随机HSV，饱和度S[0.5,1.0]，明度V[0.4,0.9]，15%概率生成淡彩色块
   - 数量：每张图片生成1个彩色块
   - 大小：占图片总面积的5%-30%

2. 文字与彩色块关系：
   - 放置：文字包围盒在彩色块内随机放置，保持5%安全边距
   - 颜色：90%白色文字，10%彩色文字（与背景形成高对比度）
   - 彩色字颜色：根据背景亮度自动选择对比色

3. 彩色块与文字尺寸关系：
   - 40%紧凑型：色块1.2-1.5倍文字包围盒
   - 50%常规型：色块1.5-3倍文字包围盒  
   - 10%宽松型：色块3倍以上文字包围盒

4. Mask生成要求：
   - 覆盖范围：只覆盖文字区域
   - 形状：遵照nature_generate_script.py的不规则文字轮廓
   - 边界限制：严格保证不超出彩色块边界

5. 输出要求：
   - GT图像：被mask覆盖区域显示彩色块颜色和纹理
   - 其他区域与输入图像保持一致

6. (新增) 字体字号多样性：
   - 动态字号：基于图片短边的3%-15%
   - 自适应尺寸：确保在不同分辨率下视觉一致性
   - 避免极端情况：字号既不会太小也不会太大

7. (更新) Mask策略多样性：
   - 30%全部涂抹：整行文字全覆盖
   - 40%部分涂抹：随机选择30%-70%的字符
   - 30%单个涂抹：只涂抹1个字符（最难样本）
   - 所有mask严格限制在彩色块内部

基于nature_generate_script.py的结构，复用其字体、字号、mask生成方式
"""

import os
import random
import shutil
import math
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from render_font_proj import (
    load_font_from_tif, resize_image_with_aspect_ratio,
    create_blank_a4, generate_text_lines, render_text,
    create_combined_image
)
from multiprocessing import Manager, Pool
import colorsys

def init_shared_state(image_files):
    manager = Manager()
    shared = manager.Namespace()
    shared.image_index = 0
    shared.image_path_cache = []
    shared.image_files = image_files
    shared.total_images = len(image_files)
    shared.lock = manager.Lock()
    # 添加实际生成数量计数器
    shared.actual_generated = 0
    return shared

# 配置参数 - 根据数据集制作规范调整
# 获取脚本所在目录，然后构建相对于项目根目录的路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))  # 向上两级到项目根目录

FONT_PATH = os.path.join(project_root, "resource", "font")  # 字体文件夹路径
IMAGE_PATH = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/nature/"  # 背景图片文件夹路径
OUTPUT_PATH = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/nature_colorblock_dataset111"  # 输出文件夹路径

# 如果需要使用相对路径，请根据实际情况修改以下路径：
# IMAGE_PATH = os.path.join(project_root, "resource", "nature_images")
# OUTPUT_PATH = os.path.join(project_root, "output", "nature_colorblock_dataset")

# 字体类型及其抽取概率（复用原脚本配置）
# 暂时禁用繁体中文字体，因为字体文件不足
FONT_PROB = {
    "TraditionalChinese": 0,  # 暂时禁用，因为字体文件不足
    "SimplifiedChinese": 6,   # 增加简体中文的权重
    "Number": 3,
    "Letter": 2,
    "Punctuation": 2
}

# 样本生成参数
SAMPLES_PER_FOLDER = 1500  # 每个文件夹需要生成的样本数
# 字号现在基于图片短边的百分比，增大基准字号（原来3%-15%，现在8%-25%，相当于增大10号左右）
FONT_SIZE_RATIO_RANGE = (0.08, 0.25)  # 字号占图片短边的8%-25%，部分字可达图片宽度的1/5
LINE_SPACING_RANGE = (10, 30)  # 行间距范围
CHAR_SPACING_RANGE = (3, 8)  # 字符间距范围

# 彩色块配置参数 - 减小色块面积，让其更贴近文字大小
COLORBLOCK_AREA_RATIO_RANGE = (0.02, 0.12)  # 彩色块面积占图片面积比例，从5%-30%减小到2%-12%
COLORBLOCK_SHAPE_PROB = {
    'rounded_rect': 0.6,  # 圆角矩形
    'ellipse': 0.3,       # 椭圆/圆形
    'polygon': 0.1        # 随机多边形
}

# 文字颜色配置
WHITE_TEXT_PROB = 0.9  # 白色文字概率
COLORED_TEXT_PROB = 0.1  # 彩色文字概率

# 彩色块与文字尺寸关系配置 - 让色块更贴近文字大小
SIZE_RELATION_PROB = {
    'compact': (0.6, 1.1, 1.3),    # 60%概率，1.1-1.3倍，更紧凑
    'normal': (0.3, 1.3, 1.8),     # 30%概率，1.3-1.8倍，减小倍数
    'loose': (0.1, 1.8, 2.5)       # 10%概率，1.8-2.5倍，大幅减小
}

# Mask策略多样性配置
MASK_STRATEGY_PROB = {
    'whole_line': 0.3,      # 30%全部涂抹
    'partial_chars': 0.4,   # 40%部分涂抹
    'single_char': 0.3      # 30%单个涂抹
}

# 部分涂抹时的字符选择比例
PARTIAL_CHARS_RATIO_RANGE = (0.3, 0.7)  # 30%-70%的字符

def collect_font_paths():
    """收集所有字体文件路径，复用原脚本逻辑"""
    font_paths = {
        "TraditionalChinese": [],
        "SimplifiedChinese": [],
        "Number": [],
        "Letter": [],
        "Punctuation": []
    }

    # 收集 TraditionalChinese 和 SimplifiedChinese 的字体
    for font_type in ["TraditionalChinese", "SimplifiedChinese"]:
        font_dir = os.path.join(FONT_PATH, font_type)
        if not os.path.exists(font_dir):
            continue

        for root, _, files in os.walk(font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.otf')):
                    font_paths[font_type].append(os.path.join(root, file))

    # 收集 Other 文件夹下的字体，供 Number 和 Letter 使用
    other_font_dir = os.path.join(FONT_PATH, "Other")
    if os.path.exists(other_font_dir):
        for root, _, files in os.walk(other_font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.otf')):
                    font_file = os.path.join(root, file)
                    font_paths["Number"].append(font_file)
                    font_paths["Letter"].append(font_file)
                    font_paths["Punctuation"].append(font_file)
    return font_paths

def sample_fonts(font_paths, num_samples):
    """根据概率抽样字体，复用原脚本逻辑，智能处理字体不足的情况"""
    # 只考虑有可用字体的类型
    available_font_types = {font_type: prob for font_type, prob in FONT_PROB.items()
                           if font_paths.get(font_type, [])}

    if not available_font_types:
        print("错误: 没有任何可用的字体类型")
        return []

    total_prob = sum(available_font_types.values())
    type_samples = {}

    # 计算每种字体类型的样本数（只考虑可用的字体类型）
    for font_type, prob in available_font_types.items():
        count = int(num_samples * (prob / total_prob))
        type_samples[font_type] = count

    # 补足因浮点数计算导致的样本数差额
    remaining = num_samples - sum(type_samples.values())
    if remaining > 0:
        # 将剩余的样本分配给第一个可用的字体类型
        first_available_type = next(iter(available_font_types))
        type_samples[first_available_type] += remaining

    # 抽样并保存为元组 (font_type, font_path)
    sampled_font_pairs = []
    for font_type, count in type_samples.items():
        available_fonts = font_paths.get(font_type, [])
        if not available_fonts:
            print(f"警告: 字体类型 {font_type} 没有可用字体")
            continue

        if count <= 0:
            continue

        # 如果字体数量不足，允许重复抽样
        try:
            if count > len(available_fonts):
                sampled = random.choices(available_fonts, k=count)
            else:
                sampled = random.sample(available_fonts, k=count)
        except Exception as e:
            print(f"警告: 字体抽样失败，类型 {font_type}, 需要 {count} 个，可用 {len(available_fonts)} 个: {e}")
            continue

        # 处理字体路径并配对
        for path in sampled:
            if not path or not isinstance(path, str):
                print(f"警告: 无效的字体路径: {path}")
                continue
            # 如果是TTC字体文件，保持原路径不变（TTC文件可以直接使用）
            # 原来的 path[0] 逻辑是错误的，会导致路径变成单个字符
            processed_path = path
            sampled_font_pairs.append((font_type, processed_path))

    if len(sampled_font_pairs) < num_samples:
        print(f"警告: 字体抽样不足，需要 {num_samples} 个，只抽到 {len(sampled_font_pairs)} 个")

    return sampled_font_pairs

def collect_image_files(image_dir):
    """收集图片文件，复用原脚本逻辑"""
    print(f"正在收集图片文件，目录: {image_dir}")

    if not os.path.exists(image_dir):
        print(f"错误: 目录不存在: {image_dir}")
        return []

    if not os.path.isdir(image_dir):
        print(f"错误: 路径不是目录: {image_dir}")
        return []

    image_files = []
    for root, _, files in os.walk(image_dir):
        print(f"正在扫描目录: {root}, 文件数: {len(files)}")
        for file in files:
            if file.lower().endswith(('.jpg', '.png', '.jpeg')):
                full_path = os.path.join(root, file)
                image_files.append(full_path)
                if len(image_files) <= 3:  # 只显示前3个文件用于调试
                    print(f"  找到图片: {full_path}")

    print(f"总共收集到 {len(image_files)} 个图片文件")
    return image_files

def generate_colorblock_color():
    """
    生成彩色块颜色
    85%概率：饱和度S[0.5,1.0]，明度V[0.4,0.9]
    15%概率：淡彩色块S[0.2,0.5]，V[0.8,1.0]
    """
    if random.random() < 0.15:  # 15%概率生成淡彩色块
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.2, 0.5)
        v = random.uniform(0.8, 1.0)
    else:  # 85%概率生成鲜艳色块
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.5, 1.0)
        v = random.uniform(0.4, 0.9)
    
    rgb = colorsys.hsv_to_rgb(h, s, v)
    return tuple(int(c * 255) for c in rgb)

def calculate_brightness(color):
    """计算颜色亮度"""
    r, g, b = color
    return (0.299 * r + 0.587 * g + 0.114 * b) / 255.0

def generate_contrast_text_color(bg_color):
    """根据背景色生成高对比度的文字颜色"""
    bg_brightness = calculate_brightness(bg_color)

    if bg_brightness > 0.5:  # 背景亮色，生成暗色文字
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.3, 1.0)
        v = random.uniform(0.1, 0.4)  # 暗色
    else:  # 背景暗色，生成亮色文字
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.3, 1.0)
        v = random.uniform(0.6, 1.0)  # 亮色

    rgb = colorsys.hsv_to_rgb(h, s, v)
    return tuple(int(c * 255) for c in rgb)

def calculate_dynamic_font_sizes(image_size, num_lines, colorblock_size=None, shape_type='rounded_rect', chars_per_line=4, use_tight_fit=False):
    """
    基于彩色块大小计算动态字号，根据边界策略调整字体大小
    40%贴边模式（更贴合），60%安全边距模式
    """
    if colorblock_size:
        # 基于彩色块大小计算字号，确保文字能放入色块
        cb_width, cb_height = colorblock_size

        # 根据传入的边界策略调整安全边距
        if use_tight_fit:
            # 完全填满模式：文字尽可能填满整个色块
            if shape_type == 'polygon':
                margin_ratio = 0.05  # 不规则图形稍微保守，5%边距
            else:
                margin_ratio = 0.02   # 规则图形基本填满，2%边距
        else:
            # 安全边距模式：保持适当距离，确保文字完全在色块内
            if shape_type == 'polygon':
                margin_ratio = 0.20  # 不规则图形使用较大安全边距
            else:
                margin_ratio = 0.12  # 规则图形使用适中安全边距

        available_width = cb_width * (1 - 2 * margin_ratio)
        available_height = cb_height * (1 - 2 * margin_ratio)

        # 基于可用宽度计算字号（考虑字符间距）
        char_spacing = 5  # 字符间距
        char_spacing_total = (chars_per_line - 1) * char_spacing
        available_width_for_chars = available_width - char_spacing_total
        font_size_by_width = int(available_width_for_chars / (chars_per_line * 0.8))  # 0.8是字符宽度系数

        # 基于可用高度计算字号（考虑行间距）
        line_spacing = 15  # 行间距
        line_spacing_total = (num_lines - 1) * line_spacing
        available_height_for_chars = available_height - line_spacing_total
        font_size_by_height = int(available_height_for_chars / (num_lines * 1.2))  # 1.2是字符高度系数

        if use_tight_fit:
            # 贴边模式：优先让文字填满色块，取较大值但不超过限制
            base_font_size = max(font_size_by_width, font_size_by_height)
            # 但仍要确保不会导致任何字符超出50%
            max_safe_size = min(font_size_by_width * 1.5, font_size_by_height * 1.5)  # 允许一定超出
            base_font_size = min(base_font_size, max_safe_size)
        else:
            # 安全边距模式：取较小值确保文字完全放入
            base_font_size = min(font_size_by_width, font_size_by_height)

        # 确保字号在合理范围内
        base_font_size = max(20, min(base_font_size, 350))  # 最小20px，最大350px

        print(f"色块尺寸: {cb_width}x{cb_height}, 形状: {shape_type}, 安全边距: {margin_ratio*100:.0f}%")
        print(f"可用区域: {available_width:.1f}x{available_height:.1f}")
        print(f"基于宽度计算字号: {font_size_by_width}, 基于高度计算字号: {font_size_by_height}")
        print(f"最终基础字号: {base_font_size}")

    else:
        # 基于图片短边计算字号（备用方案）
        base_size = min(image_size)
        base_font_size = int(base_size * random.uniform(*FONT_SIZE_RATIO_RANGE))
        base_font_size = max(24, min(base_font_size, 400))

    font_sizes = []

    for _ in range(num_lines):
        # 在基础字号基础上添加较小的随机变化（±10%），让文字更一致更大
        variation = random.uniform(0.9, 1.1)
        font_size = int(base_font_size * variation)
        # 确保字号在合理范围内
        font_size = max(24, min(font_size, 350))
        font_sizes.append(font_size)

    return font_sizes

def choose_mask_strategy():
    """选择Mask策略"""
    rand = random.random()
    cumulative = 0

    for strategy, prob in MASK_STRATEGY_PROB.items():
        cumulative += prob
        if rand <= cumulative:
            return strategy

    # 默认返回部分涂抹
    return 'partial_chars'

def choose_size_relation():
    """选择彩色块与文字的尺寸关系"""
    rand = random.random()
    cumulative = 0

    for relation_type, (prob, min_ratio, max_ratio) in SIZE_RELATION_PROB.items():
        cumulative += prob
        if rand <= cumulative:
            ratio = random.uniform(min_ratio, max_ratio)
            return relation_type, ratio

    # 默认返回常规型
    return 'normal', random.uniform(1.5, 3.0)

def calculate_text_actual_bbox(lines, line_spacing=15, char_spacing=5):
    """
    计算文字的实际渲染边界框

    参数:
        lines: 文本行列表
        line_spacing: 行间距
        char_spacing: 字符间距

    返回:
        dict: {'width': 宽度, 'height': 高度, 'min_x': 最小x, 'max_x': 最大x, 'min_y': 最小y, 'max_y': 最大y}
    """
    if not lines:
        return None

    min_x = float('inf')
    max_x = float('-inf')
    min_y = float('inf')
    max_y = float('-inf')

    current_y = 0

    for line_idx, line in enumerate(lines):
        if not line['chars']:
            continue

        # 计算行高度
        line_height = max(char['height'] for char in line['chars'])

        # 计算行宽度
        line_width = 0
        current_x = 0

        for char_idx, char in enumerate(line['chars']):
            # 获取字体对象
            line_font = line['font']

            # 使用字体的getbbox方法获取字符的实际渲染边界
            char_bbox = line_font.getbbox(char['char'])
            bbox_left, bbox_top, bbox_right, bbox_bottom = char_bbox

            # 计算字符的实际渲染边界
            char_left = current_x + bbox_left
            char_right = current_x + bbox_right
            char_top = current_y + line_height + bbox_top  # 基线位置 + bbox偏移
            char_bottom = current_y + line_height + bbox_bottom

            # 更新总边界
            min_x = min(min_x, char_left)
            max_x = max(max_x, char_right)
            min_y = min(min_y, char_top)
            max_y = max(max_y, char_bottom)

            # 移动到下一个字符位置
            current_x += char['width'] + char_spacing

        # 移动到下一行
        current_y += line_height + line_spacing

    if min_x == float('inf'):
        return None

    return {
        'width': max_x - min_x,
        'height': max_y - min_y,
        'min_x': min_x,
        'max_x': max_x,
        'min_y': min_y,
        'max_y': max_y
    }

def generate_colorblock_for_text(text_width, text_height, shape_type, use_tight_fit, image_size):
    """
    根据文字大小生成合适的彩色块

    参数:
        text_width: 文字宽度
        text_height: 文字高度
        shape_type: 色块形状类型
        use_tight_fit: 是否使用紧贴模式
        image_size: 图像尺寸

    返回:
        (cb_width, cb_height, cb_x, cb_y, shape_mask)
    """
    img_width, img_height = image_size

    # 根据边界策略计算色块大小
    if use_tight_fit:
        # 完全填满模式：色块刚好包围文字，留少量边距
        if shape_type == 'polygon':
            margin_ratio = 0.1  # 不规则图形留10%边距
        else:
            margin_ratio = 0.05  # 规则图形留5%边距
    else:
        # 安全边距模式：色块比文字大一些
        if shape_type == 'polygon':
            margin_ratio = 0.3  # 不规则图形留30%边距
        else:
            margin_ratio = 0.2   # 规则图形留20%边距

    # 计算色块尺寸
    cb_width = int(text_width * (1 + 2 * margin_ratio))
    cb_height = int(text_height * (1 + 2 * margin_ratio))

    # 确保色块不超出图像边界
    max_width = img_width - 20  # 留10px边距
    max_height = img_height - 20

    if cb_width > max_width or cb_height > max_height:
        # 如果色块太大，按比例缩小
        scale = min(max_width / cb_width, max_height / cb_height)
        cb_width = int(cb_width * scale)
        cb_height = int(cb_height * scale)

    # 随机选择色块位置
    cb_x = random.randint(10, img_width - cb_width - 10)
    cb_y = random.randint(10, img_height - cb_height - 10)

    # 创建形状mask
    from PIL import Image, ImageDraw
    shape_mask = Image.new('L', (cb_width, cb_height), 0)
    draw = ImageDraw.Draw(shape_mask)

    if shape_type == 'rounded_rect':
        # 圆角矩形
        corner_radius = min(cb_width, cb_height) // 8
        draw.rounded_rectangle([0, 0, cb_width-1, cb_height-1], radius=corner_radius, fill=255)
    elif shape_type == 'ellipse':
        # 椭圆
        draw.ellipse([0, 0, cb_width-1, cb_height-1], fill=255)
    elif shape_type == 'polygon':
        # 随机多边形
        center_x, center_y = cb_width // 2, cb_height // 2
        radius_x, radius_y = cb_width // 2 - 5, cb_height // 2 - 5
        num_points = random.randint(5, 8)

        points = []
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            # 添加随机扰动
            r_x = radius_x * random.uniform(0.7, 1.0)
            r_y = radius_y * random.uniform(0.7, 1.0)
            px = center_x + r_x * math.cos(angle)
            py = center_y + r_y * math.sin(angle)
            points.append((px, py))

        draw.polygon(points, fill=255)

    print(f"为文字 {text_width:.1f}x{text_height:.1f} 生成色块 {cb_width}x{cb_height} (边距比例: {margin_ratio*100:.0f}%)")

    return cb_width, cb_height, cb_x, cb_y, shape_mask

def generate_colorblock_shape(image_size, area_ratio, shape_type):
    """
    生成彩色块的形状和位置
    返回：(x, y, width, height, shape_mask)
    """
    img_width, img_height = image_size
    target_area = img_width * img_height * area_ratio

    # 计算彩色块的宽高，保持合理的宽高比
    aspect_ratio = random.uniform(0.5, 2.0)  # 宽高比范围
    width = int(math.sqrt(target_area * aspect_ratio))
    height = int(target_area / width)

    # 确保不超出图像边界
    width = min(width, img_width - 20)
    height = min(height, img_height - 20)

    # 随机选择位置
    x = random.randint(10, img_width - width - 10)
    y = random.randint(10, img_height - height - 10)

    # 创建形状mask
    shape_mask = Image.new('L', (width, height), 0)
    draw = ImageDraw.Draw(shape_mask)

    if shape_type == 'rounded_rect':
        # 圆角矩形
        corner_radius = min(width, height) // 8
        draw.rounded_rectangle([0, 0, width-1, height-1], radius=corner_radius, fill=255)

    elif shape_type == 'ellipse':
        # 椭圆
        draw.ellipse([0, 0, width-1, height-1], fill=255)

    elif shape_type == 'polygon':
        # 随机多边形
        center_x, center_y = width // 2, height // 2
        radius_x, radius_y = width // 2 - 5, height // 2 - 5
        num_points = random.randint(5, 8)

        points = []
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            # 添加随机扰动
            r_x = radius_x * random.uniform(0.7, 1.0)
            r_y = radius_y * random.uniform(0.7, 1.0)
            px = center_x + r_x * math.cos(angle)
            py = center_y + r_y * math.sin(angle)
            points.append((px, py))

        draw.polygon(points, fill=255)

    return x, y, width, height, shape_mask

def estimate_text_size(font_paths, font_sizes, num_lines, chars_per_line):
    """估算文字的大小，用于确定彩色块尺寸"""
    # 使用平均字体大小和字符数来估算
    avg_font_size = sum(font_sizes) / len(font_sizes)
    avg_chars_per_line = chars_per_line if isinstance(chars_per_line, int) else sum(chars_per_line) / len(chars_per_line)

    # 估算文字区域大小（粗略估算）
    char_width = avg_font_size * 0.8  # 平均字符宽度
    char_height = avg_font_size * 1.2  # 字符高度

    text_width = int(avg_chars_per_line * char_width)
    text_height = int(num_lines * char_height)

    return text_width, text_height

def generate_text_colors(num_lines, colorblock_color):
    """生成文字颜色列表"""
    text_colors = []

    for _ in range(num_lines):
        if random.random() < WHITE_TEXT_PROB:
            # 90%概率生成白色文字
            text_colors.append((255, 255, 255))
        else:
            # 10%概率生成与背景对比的彩色文字
            text_colors.append(generate_contrast_text_color(colorblock_color))

    return text_colors

def generate_samples_task(args):
    """生成样本的任务函数，基于原脚本结构修改"""
    output_folder, shared, samples_to_generate, process_id = args
    if not shared:
        raise ValueError("Shared list is empty in process %d" % process_id)

    # 检查共享状态
    if not hasattr(shared, 'image_files') or not shared.image_files:
        raise ValueError(f"[进程 {process_id}] 共享图片列表为空")

    if not hasattr(shared, 'total_images') or shared.total_images <= 0:
        raise ValueError(f"[进程 {process_id}] 图片总数无效: {getattr(shared, 'total_images', 'None')}")

    print(f"[进程 {process_id}] 开始生成，图片总数: {shared.total_images}")

    # 收集字体文件
    font_paths = collect_font_paths()

    # 检查字体可用性
    total_fonts = sum(len(fonts) for fonts in font_paths.values())
    if total_fonts == 0:
        print(f"[进程 {process_id}] 错误: 没有找到任何可用字体")
        return

    print(f"[进程 {process_id}] 可用字体统计:")
    for font_type, fonts in font_paths.items():
        print(f"  {font_type}: {len(fonts)} 个字体")

    # 如果字体总数太少，调整生成策略
    if total_fonts < 10:
        print(f"[进程 {process_id}] 警告: 字体数量较少({total_fonts}个)，可能影响生成质量")

    generated = 0
    failed_attempts = 0
    max_failed_attempts = samples_to_generate * 2  # 允许失败次数为目标样本数的2倍

    while generated < samples_to_generate and failed_attempts < max_failed_attempts:
        try:
            # 选择图片
            with shared.lock:
                # 再次检查以防竞态条件
                if not shared.image_files or shared.total_images <= 0:
                    raise ValueError(f"[进程 {process_id}] 共享状态异常: 图片列表长度={len(shared.image_files) if shared.image_files else 0}, 总数={shared.total_images}")

                current_index = shared.image_index
                shared.image_index += 1
                image_path = shared.image_files[current_index % shared.total_images]

            # 检查图片文件是否存在和有效
            if not os.path.exists(image_path):
                print(f"[进程 {process_id}] 图片文件不存在: {image_path}")
                failed_attempts += 1
                continue

            try:
                # 加载背景图片获取尺寸
                bg_image = Image.open(image_path)
                image_size = bg_image.size
                bg_image.close()  # 及时关闭图片
            except Exception as e:
                print(f"[进程 {process_id}] 无法加载图片 {image_path}: {e}")
                failed_attempts += 1
                continue

            # 全新的生成逻辑：先生成文字，再根据文字大小生成色块
            print("=== 开始新的文字优先生成逻辑 ===")

            # 1. 先确定文字配置
            # 选择彩色块形状
            shape_type = random.choices(
                list(COLORBLOCK_SHAPE_PROB.keys()),
                weights=list(COLORBLOCK_SHAPE_PROB.values())
            )[0]

            # 决定文字边界策略：40%完全填满模式，60%保持安全边距
            use_tight_fit = random.random() < 0.4  # 40%概率使用完全填满模式
            fit_mode = "完全填满模式" if use_tight_fit else "安全边距模式"
            print(f"边界策略: {fit_mode}")

            # 根据形状类型确定文字配置
            if shape_type == 'polygon':
                # 不规则图形：只生成1行文字
                num_lines = 1
                chars_per_line = random.randint(2, 4)
            else:
                # 规则图形：可以生成多行文字
                num_lines = random.randint(2, 4)
                chars_per_line = random.randint(3, 6)

            print(f"文字配置: {num_lines}行 x {chars_per_line}字符, 形状: {shape_type}")

            # 2. 随机抽样字体并生成文字内容
            fonts_type = sample_fonts(font_paths, num_lines)

            # 检查字体抽样结果
            if not fonts_type:
                print(f"[进程 {process_id}] 警告: 字体抽样失败，跳过该样本")
                failed_attempts += 1
                continue

            if len(fonts_type) < num_lines:
                print(f"[进程 {process_id}] 警告: 字体数量不足，需要{num_lines}个，只有{len(fonts_type)}个，跳过该样本")
                failed_attempts += 1
                continue

            # 3. 使用大字体！基于图像尺寸计算字体大小
            base_size = min(image_size)
            font_sizes = []
            for _ in range(num_lines):
                # 每行字体大小在合理范围内随机 - 使用原来的大字体配置
                font_size = int(base_size * random.uniform(*FONT_SIZE_RATIO_RANGE))
                font_size = max(24, min(font_size, 350))  # 限制在合理范围
                font_sizes.append(font_size)

            print(f"确定大字体大小: {font_sizes} (基于图像短边: {base_size}, 比例: {FONT_SIZE_RATIO_RANGE})")

            # 4. 加载最终字体
            fonts = []
            for i in range(num_lines):
                try:
                    if i >= len(fonts_type):
                        print(f"[进程 {process_id}] 错误: 字体索引{i}超出范围，fonts_type长度为{len(fonts_type)}")
                        break

                    if len(fonts_type[i]) < 2:
                        print(f"[进程 {process_id}] 错误: 字体元组格式错误，索引{i}: {fonts_type[i]}")
                        break

                    font_path = fonts_type[i][1]
                    font_size = font_sizes[i]
                    font = load_font_from_tif(font_path, font_size)
                    fonts.append([fonts_type[i][0], font])
                except Exception as e:
                    print(f"[进程 {process_id}] 加载字体失败，索引{i}: {e}")
                    break

            if len(fonts) < num_lines:
                print(f"[进程 {process_id}] 警告: 字体加载不完整，跳过该样本")
                failed_attempts += 1
                continue

            # 5. 生成最终文本行
            lines = generate_text_lines(num_lines, chars_per_line, fonts)

            # 6. 计算文字的实际渲染边界
            text_bbox = calculate_text_actual_bbox(lines, line_spacing=15, char_spacing=5)
            if not text_bbox:
                print("警告: 无法计算文字边界，跳过该样本")
                failed_attempts += 1
                continue

            text_width, text_height = text_bbox['width'], text_bbox['height']
            print(f"文字实际渲染尺寸: {text_width:.1f} x {text_height:.1f}")

            # 7. 根据最终文字大小生成合适的色块（不再重新计算字体）
            cb_width, cb_height, cb_x, cb_y, shape_mask = generate_colorblock_for_text(
                text_width, text_height, shape_type, use_tight_fit, image_size
            )

            if cb_width is None:
                print("警告: 无法生成合适的色块来包围文字，跳过该样本")
                failed_attempts += 1
                continue

            print(f"生成色块尺寸: {cb_width} x {cb_height}, 位置: ({cb_x}, {cb_y})")
            print("✅ 逻辑修复：文字大小确定后生成色块，避免重复计算导致的尺寸不匹配")

            print(f"成功生成 {len(lines)} 行文字，每行字符数: {[len(line['chars']) for line in lines]}")

            line_spacing = random.randint(*LINE_SPACING_RANGE)
            char_spacing = random.randint(*CHAR_SPACING_RANGE)

            print(f"色块尺寸: {cb_width}x{cb_height}, 形状: {shape_type}")
            print(f"文字配置: {num_lines}行 x {chars_per_line}字符, 平均字号: {sum(font_sizes)/len(font_sizes):.1f}")

            # 生成彩色块颜色
            colorblock_color = generate_colorblock_color()

            # 生成文字颜色
            text_colors = generate_text_colors(num_lines, colorblock_color)

            # 选择Mask策略
            mask_strategy = choose_mask_strategy()

            # 调用修改后的生成函数（使用确定的字体和文字）
            success = generate_colorblock_erasure_dataset(
                output_folder,
                1,
                current_index,
                fonts_type,  # 传递字体类型信息
                font_sizes,  # 使用确定的字体大小
                None,
                dpi=300,
                erasure_mode=mask_strategy,  # 使用新的mask策略
                expand_boundary=True,
                multi_line_erasure=True,
                left_right_expand_boundary_ratio=0.3,
                line_spacing=line_spacing,
                char_spacing=char_spacing,
                num_lines=num_lines,
                chars_per_line=chars_per_line,
                use_image_background=True,
                image_paths=image_path,
                text_colors=text_colors,
                img_name=os.path.basename(image_path),
                target_size=image_size,  # 使用实际图像尺寸而不是固定尺寸
                # 新增彩色块参数
                colorblock_info={
                    'x': cb_x, 'y': cb_y, 'width': cb_width, 'height': cb_height,
                    'color': colorblock_color, 'shape_mask': shape_mask, 'shape_type': shape_type,
                    'use_tight_fit': use_tight_fit,  # 传递边界策略
                    'pregenerated_lines': lines,  # 传递预生成的文字行
                    'final_fonts': fonts  # 传递最终确定的字体对象
                }
            )

            if success:
                generated += 1
                # 更新共享计数器
                with shared.lock:
                    shared.actual_generated += 1
                # 进度显示
                if generated % 10 == 0:
                    print(f"[进程 {process_id}] 已生成 {generated}/{samples_to_generate} 个样本")
            else:
                failed_attempts += 1

        except Exception as e:
            print(f"[进程 {process_id}] 生成样本时出错: {e}")
            failed_attempts += 1
            continue

    if failed_attempts >= max_failed_attempts:
        print(f"[进程 {process_id}] 警告：失败次数过多，提前结束。已生成 {generated}/{samples_to_generate} 个样本")

def check_char_overlap(new_char_bounds, existing_chars, min_spacing=2):
    """
    检查新字符是否与已存在的字符重叠

    参数:
        new_char_bounds: 新字符的边界 (left, top, right, bottom)
        existing_chars: 已存在的字符列表
        min_spacing: 最小间距

    返回:
        bool: True表示重叠，False表示不重叠
    """
    new_left, new_top, new_right, new_bottom = new_char_bounds

    for char in existing_chars:
        if 'actual_left' in char:
            char_left = char['actual_left']
            char_right = char['actual_right']
            char_top = char['actual_top']
            char_bottom = char['actual_bottom']
        else:
            char_left = char['x']
            char_right = char['x'] + char['width']
            char_top = char['y'] - char['height']
            char_bottom = char['y']

        # 检查水平重叠
        horizontal_overlap = not (new_right + min_spacing <= char_left or new_left >= char_right + min_spacing)
        # 检查垂直重叠
        vertical_overlap = not (new_bottom + min_spacing <= char_top or new_top >= char_bottom + min_spacing)

        if horizontal_overlap and vertical_overlap:
            return True

    return False

def filter_chars_outside_ellipse(positioned_lines, ellipse_x, ellipse_y, ellipse_width, ellipse_height):
    """
    过滤掉椭圆外的字符

    参数:
        positioned_lines: 定位好的文字行
        ellipse_x, ellipse_y: 椭圆左上角坐标
        ellipse_width, ellipse_height: 椭圆宽度和高度

    返回:
        过滤后的positioned_lines
    """
    # 椭圆中心和半轴长度
    center_x = ellipse_x + ellipse_width / 2
    center_y = ellipse_y + ellipse_height / 2
    a = ellipse_width / 2  # 水平半轴
    b = ellipse_height / 2  # 垂直半轴

    # 安全边距（避免字符贴边）
    safety_margin = 10
    safe_a = max(a - safety_margin, a * 0.8)  # 至少保留80%
    safe_b = max(b - safety_margin, b * 0.8)

    print(f"椭圆中心: ({center_x:.1f}, {center_y:.1f}), 安全半轴: {safe_a:.1f}x{safe_b:.1f}")

    filtered_lines = []
    total_chars_before = 0
    total_chars_after = 0

    for line in positioned_lines:
        filtered_chars = []
        total_chars_before += len(line['chars'])

        for char in line['chars']:
            # 使用字符的实际渲染边界（如果有的话）
            if 'actual_left' in char and 'actual_right' in char and 'actual_top' in char and 'actual_bottom' in char:
                char_left = char['actual_left']
                char_right = char['actual_right']
                char_top = char['actual_top']
                char_bottom = char['actual_bottom']
            else:
                # 回退到基本计算
                char_left = char['x']
                char_right = char['x'] + char['width']
                char_top = char['y'] - char['height']
                char_bottom = char['y']

            # 检查字符的四个角是否都在椭圆内
            corners = [
                (char_left, char_top),      # 左上角
                (char_right, char_top),     # 右上角
                (char_left, char_bottom),   # 左下角
                (char_right, char_bottom)   # 右下角
            ]

            # 检查所有角是否都在椭圆内
            all_corners_inside = True
            for corner_x, corner_y in corners:
                # 椭圆方程: ((x-cx)/a)^2 + ((y-cy)/b)^2 <= 1
                dx = (corner_x - center_x) / safe_a
                dy = (corner_y - center_y) / safe_b
                if dx*dx + dy*dy > 1.0:
                    all_corners_inside = False
                    break

            if all_corners_inside:
                filtered_chars.append(char)
            else:
                print(f"  移除椭圆外字符: '{char['char']}' at ({char['x']}, {char['y']})")

        # 只保留有字符的行
        if filtered_chars:
            filtered_line = line.copy()
            filtered_line['chars'] = filtered_chars
            filtered_lines.append(filtered_line)
            total_chars_after += len(filtered_chars)

    print(f"椭圆过滤统计: {total_chars_before} -> {total_chars_after} 字符")
    return filtered_lines


def calculate_text_positions_in_colorblock(lines, cb_x, cb_y, cb_width, cb_height,
                                          line_spacing=10, char_spacing=5, shape_type='rounded_rect', use_tight_fit=False):
    """
    计算文本位置，40%贴边模式（更贴合），60%安全边距模式

    修复说明：统一坐标系统为渲染位置（左上角），与原始脚本保持一致，避免mask偏移问题
    新增重叠检测和避免机制，确保字符不会重叠

    参数:
        lines: 文本行列表
        cb_x, cb_y, cb_width, cb_height: 彩色块的位置和尺寸
        line_spacing: 行间距
        char_spacing: 字符间距
        shape_type: 彩色块形状类型
        use_tight_fit: 是否使用贴边模式（文字基本贴着色块边缘）

    返回:
        positioned_lines: 带位置信息的文本行列表
    """
    positioned_lines = []
    all_positioned_chars = []  # 跟踪所有已定位的字符，用于重叠检测

    # 根据传入的边界策略调整安全边距
    if use_tight_fit:
        # 完全填满模式：文字尽可能填满整个色块，允许接触边界
        if shape_type == 'polygon':
            # 不规则图形稍微保守，确保视觉上在色块内
            margin_ratio = 0.05  # 5%最小边距，几乎填满
        else:
            # 规则图形完全填满，允许文字接触色块边界
            margin_ratio = 0.02   # 2%最小边距，基本填满整个色块
        fit_mode = "完全填满模式(接触边界)"
    else:
        # 安全边距模式：保持适当距离，确保文字完全在色块内
        if shape_type == 'polygon':
            # 不规则图形使用较大的安全边距
            margin_ratio = 0.20  # 20%安全边距，确保视觉上完全在内
        else:
            # 规则图形使用适中的安全边距
            margin_ratio = 0.12  # 12%安全边距，保持适当距离
        fit_mode = "安全边距模式(保守)"

    margin = min(cb_width, cb_height) * margin_ratio
    available_x = cb_x + margin
    available_y = cb_y + margin
    available_width = cb_width - 2 * margin
    available_height = cb_height - 2 * margin

    print(f"文字定位: 形状={shape_type}, 模式={fit_mode}, 安全边距={margin_ratio*100:.1f}%, 可用区域={available_width:.1f}x{available_height:.1f}")

    # 计算总文本高度
    total_text_height = 0
    for line in lines:
        if line['chars']:
            line_height = max(char['height'] for char in line['chars'])
            total_text_height += line_height

    # 添加行间距
    if len(lines) > 1:
        total_text_height += (len(lines) - 1) * line_spacing

    # 检查是否能放入彩色块
    if total_text_height > available_height:
        print(f"警告：文字高度 {total_text_height} 超过彩色块可用高度 {available_height}")
        print(f"尝试缩小字体或减少行数来适应色块")
        # 不直接返回空列表，而是尝试强制适应
        # 可以通过缩放或减少行数来适应
        scale_factor = available_height / total_text_height * 0.9  # 留10%余量
        print(f"应用缩放因子: {scale_factor:.2f}")
        # 这里暂时继续处理，让后续逻辑处理字符超出的情况

    # 计算起始Y位置（垂直居中）
    start_y = available_y + (available_height - total_text_height) / 2

    current_y = start_y

    for line_idx, line in enumerate(lines):
        if not line['chars']:
            continue

        # 计算行高度
        line_height = max(char['height'] for char in line['chars'])

        # 计算行宽度
        line_width = sum(char['width'] for char in line['chars'])
        line_width += (len(line['chars']) - 1) * char_spacing

        # 检查行宽度是否超出彩色块
        if line_width > available_width:
            print(f"警告：第{line_idx}行宽度 {line_width} 超过彩色块可用宽度 {available_width}")
            return []

        # 计算起始X位置（水平居中）
        start_x = available_x + (available_width - line_width) / 2

        # 为每个字符分配位置，严格限制在色块边界内
        current_x = start_x
        positioned_chars = []

        # 计算该行所有字符的底边对齐基准线（基线位置）
        char_line_bottom = current_y + line_height

        print(f"🔍 开始处理第{line_idx}行，共{len(line['chars'])}个字符")
        print(f"   行起始位置: start_x={start_x:.1f}, char_line_bottom={char_line_bottom:.1f}")
        print(f"   行宽度: {line_width:.1f}, 行高度: {line_height}")

        for char_idx, char in enumerate(line['chars']):
            print(f"   🔤 处理字符 [{char_idx}]: '{char['char']}', current_x={current_x:.1f}")

            # 获取字体对象来计算精确的字符边界
            line_font = line['font']

            # 使用字体的getbbox方法获取字符的实际渲染边界
            char_bbox = line_font.getbbox(char['char'])
            bbox_left, bbox_top, bbox_right, bbox_bottom = char_bbox
            print(f"      字符bbox: {char_bbox}")

            # 计算字符的实际渲染边界（基于基线位置）
            char_left = current_x + bbox_left  # 字符实际左边界
            char_right = current_x + bbox_right  # 字符实际右边界
            char_top = char_line_bottom + bbox_top  # 字符实际顶部（基线 + bbox顶部偏移）
            char_bottom = char_line_bottom + bbox_bottom  # 字符实际底部（基线 + bbox底部偏移）

            print(f"      初始渲染边界: ({char_left:.1f}, {char_top:.1f}) -> ({char_right:.1f}, {char_bottom:.1f})")

            # 根据边界策略检查字符是否在允许范围内
            cb_right = cb_x + cb_width
            cb_bottom = cb_y + cb_height

            # 根据边界策略设置不同的检查标准
            if use_tight_fit:
                # 完全填满模式：允许文字接触色块边界，只要不超出即可
                boundary_left = cb_x
                boundary_right = cb_right
                boundary_top = cb_y
                boundary_bottom = cb_bottom
                check_mode = "完全填满模式"
            else:
                # 安全边距模式：文字必须在色块的安全区域内
                cb_margin_x = cb_width * 0.1  # 色块宽度的10%
                cb_margin_y = cb_height * 0.1  # 色块高度的10%
                boundary_left = cb_x + cb_margin_x
                boundary_right = cb_right - cb_margin_x
                boundary_top = cb_y + cb_margin_y
                boundary_bottom = cb_bottom - cb_margin_y
                check_mode = "安全边距模式"

            # 检查字符的实际渲染边界是否在允许范围内
            char_out_of_bounds = (char_left < boundary_left or char_right > boundary_right or
                                 char_top < boundary_top or char_bottom > boundary_bottom)

            if char_out_of_bounds:
                # 计算超出距离
                left_overflow = max(0, boundary_left - char_left)
                right_overflow = max(0, char_right - boundary_right)
                top_overflow = max(0, boundary_top - char_top)
                bottom_overflow = max(0, char_bottom - boundary_bottom)

                total_overflow = left_overflow + right_overflow + top_overflow + bottom_overflow
                print(f"字符 '{char['char']}' 实际渲染边界超出{check_mode}限制，超出距离: {total_overflow:.1f}px")
                print(f"  字符bbox: {char_bbox}")
                print(f"  实际渲染边界: ({char_left:.1f},{char_top:.1f})-({char_right:.1f},{char_bottom:.1f})")
                print(f"  允许边界: ({boundary_left:.1f},{boundary_top:.1f})-({boundary_right:.1f},{boundary_bottom:.1f})")

                # 关键修复：如果超出距离较小，尝试调整位置；超出过大则跳过
                if total_overflow <= 15.0:  # 允许15px的小幅超出
                    print(f"  ✅ 超出距离较小({total_overflow:.1f}px ≤ 15px)，尝试调整位置")

                    # 计算调整后的位置，确保在边界内
                    if char_right > boundary_right:
                        # 右边超出，向左调整
                        adjusted_x = boundary_right - bbox_right
                    elif char_left < boundary_left:
                        # 左边超出，向右调整
                        adjusted_x = boundary_left - bbox_left
                    else:
                        adjusted_x = current_x

                    # 确保调整后的位置仍在边界内
                    adjusted_x = max(boundary_left - bbox_left, min(adjusted_x, boundary_right - bbox_right))

                    # 更新字符边界
                    char_left = adjusted_x + bbox_left
                    char_right = adjusted_x + bbox_right

                    print(f"  调整后位置: x={adjusted_x}, 渲染边界: ({char_left:.1f},{char_top:.1f})-({char_right:.1f},{char_bottom:.1f})")

                    # 修复重叠问题：当字符位置被调整时，需要相应更新current_x以避免重叠
                    # 如果调整后的字符右边界超过了原本的current_x + char_width，则更新current_x
                    expected_char_right = current_x + char['width']
                    if char_right > expected_char_right:
                        # 字符被向右调整，需要更新current_x以避免下一个字符重叠
                        current_x = char_right - char['width']
                        print(f"  更新current_x到 {current_x} 以避免字符重叠")

                else:
                    print(f"  ❌ 超出距离过大({total_overflow:.1f}px > 30px)，跳过该字符")
                    # 🚨 关键修复：不能直接continue，需要更新current_x
                    old_current_x = current_x
                    current_x += char['width'] + char_spacing
                    print(f"      边界超出跳过字符 '{char['char']}'，current_x: {old_current_x:.1f} -> {current_x:.1f}")
                    continue  # 跳过字符但已更新current_x

            # 如果字符在允许范围内（或已调整到允许范围内），添加到位置列表
            # 存储实际渲染边界信息，用于后续的mask生成

            # 确定最终的字符渲染位置
            if char_out_of_bounds and total_overflow <= 15.0:
                # 使用调整后的位置
                final_x = adjusted_x
            else:
                # 使用原始位置
                final_x = current_x

            # 检查字符是否与已定位的字符重叠
            char_bounds = (char_left, char_top, char_right, char_bottom)
            skip_char = False

            if check_char_overlap(char_bounds, all_positioned_chars, min_spacing=3):
                print(f"  ⚠️  字符 '{char['char']}' 与已有字符重叠，尝试调整位置")

                # 尝试向右移动字符以避免重叠
                max_attempts = 10
                attempt = 0
                found_position = False

                while attempt < max_attempts:
                    # 向右移动5px
                    test_final_x = final_x + (attempt + 1) * 5
                    test_char_left = test_final_x + bbox_left
                    test_char_right = test_final_x + bbox_right
                    test_char_bounds = (test_char_left, char_top, test_char_right, char_bottom)

                    # 检查调整后是否还在色块边界内
                    if test_char_right <= cb_x + cb_width - margin:
                        if not check_char_overlap(test_char_bounds, all_positioned_chars, min_spacing=3):
                            print(f"    ✅ 成功调整字符位置，避免重叠：final_x={test_final_x}")
                            final_x = test_final_x
                            char_left = test_char_left
                            char_right = test_char_right
                            found_position = True
                            break
                    else:
                        print(f"    ❌ 调整后字符超出色块边界")
                        break  # 不再尝试更远的位置

                    attempt += 1

                if not found_position:
                    print(f"    ❌ 无法找到合适位置避免重叠，跳过字符 '{char['char']}'")
                    skip_char = True

            # 如果字符需要跳过，更新current_x但不添加字符
            if skip_char:
                old_current_x = current_x
                current_x += char['width'] + char_spacing
                print(f"      跳过字符 '{char['char']}'，current_x: {old_current_x:.1f} -> {current_x:.1f}")
                continue

            char_info = {
                'char': char['char'],
                'x': int(final_x),  # 字符基准位置（用于渲染）
                'y': int(char_line_bottom),  # 基线位置（用于渲染）
                'width': char['width'],  # 原始字符宽度
                'height': char['height'],  # 原始字符高度
                'index': char['index'],
                # 新增：实际渲染边界信息
                'actual_left': char_left,
                'actual_right': char_right,
                'actual_top': char_top,
                'actual_bottom': char_bottom,
                'bbox': char_bbox  # 保存bbox信息用于调试
            }

            positioned_chars.append(char_info)
            all_positioned_chars.append(char_info)  # 添加到全局字符列表

            # 🚨 关键修复：根据字符的实际最终位置更新current_x
            # current_x应该是下一个字符的起始位置
            old_current_x = current_x
            current_x = final_x + char['width'] + char_spacing

            print(f"      ✅ 字符 '{char['char']}' 成功添加:")
            print(f"         最终位置: x={final_x:.1f}")
            print(f"         实际边界: ({char_left:.1f}, {char_top:.1f}) -> ({char_right:.1f}, {char_bottom:.1f})")
            print(f"         current_x更新: {old_current_x:.1f} -> {current_x:.1f}")
            print(f"         已定位字符总数: {len(all_positioned_chars)}")

        # 不再强制每行保留字符，只记录空行信息
        if not positioned_chars and line['chars']:
            print(f"ℹ️  第{line_idx}行所有字符都被跳过（边界限制或重叠）")

        positioned_lines.append({
            'chars': positioned_chars,
            'x': int(start_x),
            'y': int(char_line_bottom),  # 使用基线位置
            'width': int(line_width),
            'height': int(line_height),
            'index': line['index'],
            'font': line['font']
        })

        current_y += line_height + line_spacing

    # 最终安全检查：如果所有行都没有字符，尝试强制保留一个字符
    total_chars = sum(len(line['chars']) for line in positioned_lines)
    if total_chars == 0:
        print("⚠️  警告：所有字符都被跳过，尝试强制保留一个字符以避免空数据")

        # 找到第一个有字符的行
        for line_idx, line in enumerate(lines):
            if line['chars']:
                first_char = line['chars'][0]
                line_font = line['font']
                char_bbox = line_font.getbbox(first_char['char'])

                # 在色块中心位置放置字符
                center_x = cb_x + cb_width // 2
                center_y = cb_y + cb_height // 2

                char_x = center_x - first_char['width'] // 2
                char_left = char_x + char_bbox[0]
                char_right = char_x + char_bbox[2]
                char_top = center_y + char_bbox[1]
                char_bottom = center_y + char_bbox[3]

                char_info = {
                    'char': first_char['char'],
                    'x': int(char_x),
                    'y': int(center_y),
                    'width': first_char['width'],
                    'height': first_char['height'],
                    'index': first_char['index'],
                    'actual_left': char_left,
                    'actual_right': char_right,
                    'actual_top': char_top,
                    'actual_bottom': char_bottom,
                    'bbox': char_bbox
                }

                # 找到对应的positioned_line并添加字符
                if line_idx < len(positioned_lines):
                    positioned_lines[line_idx]['chars'].append(char_info)
                else:
                    # 创建新的positioned_line
                    positioned_lines.append({
                        'chars': [char_info],
                        'x': int(char_x),
                        'y': int(center_y),
                        'width': first_char['width'],
                        'height': first_char['height'],
                        'index': line['index'],
                        'font': line['font']
                    })

                print(f"  强制保留字符 '{first_char['char']}' 在位置 ({char_x}, {center_y})")
                total_chars = 1
                break

        if total_chars == 0:
            print("❌ 严重警告：无法保留任何字符，这将导致只有色块没有文字的数据")
            print("   建议检查字体大小计算和色块尺寸是否合理")
            return []

    print(f"✅ 成功定位 {len(positioned_lines)} 行文字，总字符数: {total_chars}")

    # 特殊处理：如果是椭圆形色块，移除椭圆外的字符
    if shape_type == 'ellipse':
        print(f"🔄 椭圆形色块特殊处理：移除椭圆外的字符")
        positioned_lines = filter_chars_outside_ellipse(positioned_lines, cb_x, cb_y, cb_width, cb_height)

        # 检查过滤后是否还有足够的字符
        remaining_chars = sum(len(line['chars']) for line in positioned_lines)
        if remaining_chars < 3:  # 至少需要3个字符
            print(f"❌ 椭圆过滤后字符太少 ({remaining_chars} < 3)，返回空列表")
            return []

        print(f"✅ 椭圆过滤完成，剩余 {remaining_chars} 个字符")

    return positioned_lines



# 删除不再使用的函数

def create_colorblock_mask_image(image_size, erasure_regions, original_image):
    """
    创建彩色块专用的掩码图像，使用与原脚本一致的mask生成逻辑

    参数:
        image_size: 图像尺寸 (width, height)
        erasure_regions: 要抹除的区域列表
        original_image: 原始图像，用于生成可视化

    返回:
        mask_image: 掩码图像
        show_img: 可视化图像
    """
    # 直接使用原脚本的create_mask_image函数
    from render_font_proj import create_mask_image
    return create_mask_image(image_size, erasure_regions, original_image)

def create_colorblock_visualization_image(positioned_lines, erasure_regions, colorblock_info, image_size):
    """
    创建彩色块专用的可视化图像，显示字符位置、边框和彩色块边界

    参数:
        positioned_lines: 带位置信息的文本行列表
        erasure_regions: 要抹除的区域列表
        colorblock_info: 彩色块信息
        image_size: 图像尺寸

    返回:
        visualization_image: 可视化图像
    """
    from render_font_proj import create_visualization_image
    from PIL import ImageDraw

    # 首先使用原脚本的可视化函数
    vis_image = create_visualization_image(positioned_lines, erasure_regions, None, image_size)

    # 在可视化图像上添加彩色块边界
    if colorblock_info:
        draw = ImageDraw.Draw(vis_image)
        cb_x = colorblock_info['x']
        cb_y = colorblock_info['y']
        cb_width = colorblock_info['width']
        cb_height = colorblock_info['height']

        # 绘制彩色块边界（蓝色虚线框）
        # 由于PIL不直接支持虚线，我们绘制多个短线段来模拟虚线效果
        dash_length = 10
        gap_length = 5

        # 绘制上边
        x = cb_x
        while x < cb_x + cb_width:
            end_x = min(x + dash_length, cb_x + cb_width)
            draw.line([(x, cb_y), (end_x, cb_y)], fill=(0, 0, 255), width=2)
            x += dash_length + gap_length

        # 绘制下边
        x = cb_x
        while x < cb_x + cb_width:
            end_x = min(x + dash_length, cb_x + cb_width)
            draw.line([(x, cb_y + cb_height), (end_x, cb_y + cb_height)], fill=(0, 0, 255), width=2)
            x += dash_length + gap_length

        # 绘制左边
        y = cb_y
        while y < cb_y + cb_height:
            end_y = min(y + dash_length, cb_y + cb_height)
            draw.line([(cb_x, y), (cb_x, end_y)], fill=(0, 0, 255), width=2)
            y += dash_length + gap_length

        # 绘制右边
        y = cb_y
        while y < cb_y + cb_height:
            end_y = min(y + dash_length, cb_y + cb_height)
            draw.line([(cb_x + cb_width, y), (cb_x + cb_width, end_y)], fill=(0, 0, 255), width=2)
            y += dash_length + gap_length

    return vis_image

def create_colorblock_combined_image(original_image, gt_image, mask_image, positioned_lines, erasure_regions, colorblock_info):
    """
    创建彩色块专用的拼接图像，包含彩色块边界可视化

    参数:
        original_image: 原始图像（PIL图像）
        gt_image: GT图像（PIL图像）
        mask_image: 掩码图像（PIL图像）
        positioned_lines: 带位置信息的文本行列表
        erasure_regions: 要抹除的区域列表
        colorblock_info: 彩色块信息

    返回:
        combined_image: 拼接后的图像（PIL图像）
    """
    from render_font_proj import overlay_mask_as_transparent_green
    from PIL import Image

    # 创建图1：原图+掩码（绿色透明覆盖）
    original_with_mask = overlay_mask_as_transparent_green(original_image, mask_image)

    # 创建图2：GT图+掩码（绿色透明覆盖）
    gt_with_mask = overlay_mask_as_transparent_green(gt_image, mask_image)

    # 创建图3：彩色块专用字符边框可视化图
    vis_image = create_colorblock_visualization_image(positioned_lines, erasure_regions, colorblock_info, original_image.size)

    # 确保三张图像大小相同
    width, height = original_image.size

    # 水平拼接图1、图2和图3
    combined_image = Image.new('RGB', (width * 3, height))
    combined_image.paste(original_with_mask, (0, 0))
    combined_image.paste(gt_with_mask, (width, 0))
    combined_image.paste(vis_image, (width * 2, 0))

    return combined_image

def select_colorblock_erasure_regions(positioned_lines, erasure_mode, colorblock_info, expand_boundary=False,
                                     multi_line_erasure=True, left_right_expand_boundary_ratio=0.3):
    """
    为彩色块定制的抹除区域选择函数
    基于原脚本的字符级别选择逻辑，确保与nature_generate_script.py一致

    修复说明：统一坐标系统为渲染位置（左上角），修复mask偏移问题
    """
    erasure_regions = []

    if not positioned_lines or not colorblock_info:
        return erasure_regions

    # 获取彩色块边界
    cb_x = colorblock_info['x']
    cb_y = colorblock_info['y']
    cb_width = colorblock_info['width']
    cb_height = colorblock_info['height']
    cb_right = cb_x + cb_width
    cb_bottom = cb_y + cb_height

    # 筛选在彩色块内的文字行
    valid_lines = []
    for line_idx, line in enumerate(positioned_lines):
        if not line['chars']:
            continue

        # 检查行是否在彩色块内，使用实际渲染边界
        line_chars_in_block = []
        for char in line['chars']:
            # 使用存储的实际渲染边界信息（如果有的话）
            if 'actual_left' in char:
                # 使用精确的实际渲染边界
                char_left = char['actual_left']
                char_right = char['actual_right']
                char_top = char['actual_top']
                char_bottom = char['actual_bottom']
            else:
                # 回退到原始计算方法
                char_left = char['x']
                char_right = char['x'] + char['width']
                char_top = char['y'] - char['height']
                char_bottom = char['y']

            # 检查字符的实际渲染边界是否在彩色块内
            if (char_left < cb_right and char_right > cb_x and
                char_top < cb_bottom and char_bottom > cb_y):
                line_chars_in_block.append(char)

        if line_chars_in_block:
            valid_lines.append({
                'line_idx': line_idx,
                'line': line,
                'chars_in_block': line_chars_in_block
            })

    if not valid_lines:
        return erasure_regions

    # 确定要处理的行数（与原脚本逻辑一致）
    if multi_line_erasure:
        num_lines_to_erase = random.randint(1, len(valid_lines))
    else:
        num_lines_to_erase = 1

    # 随机选择要处理的行
    selected_lines = random.sample(valid_lines, num_lines_to_erase)

    # 对每行应用mask策略（与原脚本逻辑一致）
    for line_info in selected_lines:
        line_idx = line_info['line_idx']
        line = line_info['line']
        chars_in_block = line_info['chars_in_block']

        if not chars_in_block:
            continue

        # 为每行随机选择抹除模式（与原脚本一致）
        if erasure_mode == 'random':
            line_mode = random.choice(['single_char', 'whole_line', 'partial_line'])
        elif erasure_mode == 'whole_line':
            line_mode = 'whole_line'
        elif erasure_mode == 'partial_chars':
            line_mode = 'partial_line'
        elif erasure_mode == 'single_char':
            line_mode = 'single_char'
        else:
            line_mode = 'single_char'

        # 根据模式选择要抹除的字符（与原脚本逻辑一致）
        if line_mode == 'single_char':
            # 随机选择一个字符
            selected_chars = [random.choice(chars_in_block)]

        elif line_mode == 'whole_line':
            # 整行字符
            selected_chars = chars_in_block

        elif line_mode == 'partial_line':
            # 部分字符：随机选择连续的字符段
            if len(chars_in_block) <= 1:
                selected_chars = chars_in_block
            else:
                # 随机选择起始和结束位置
                start_idx = random.randint(0, len(chars_in_block) - 1)
                end_idx = random.randint(start_idx, len(chars_in_block) - 1)
                selected_chars = chars_in_block[start_idx:end_idx + 1]
        else:
            selected_chars = chars_in_block

        if selected_chars:
            # 终极修复：基于字符实际渲染位置计算mask区域
            first_char = selected_chars[0]
            last_char = selected_chars[-1]

            # 计算mask区域的位置和尺寸
            region_x = first_char['x']
            region_width = (last_char['x'] + last_char['width']) - first_char['x']

            # 关键修复：计算字符的实际渲染边界
            # 获取字体对象来计算精确的字符边界
            line_font = line['font']

            if line_mode == 'single_char':
                # 单字符模式：使用该字符的实际渲染边界
                char_bbox = line_font.getbbox(first_char['char'])
                # 字符实际顶部位置 = 基线位置 + bbox顶部偏移
                actual_top = first_char['y'] + char_bbox[1]
                # 字符实际底部位置 = 基线位置 + bbox底部偏移
                actual_bottom = first_char['y'] + char_bbox[3]

                region_y = actual_bottom  # create_mask_image期望的是底部位置
                region_height = actual_bottom - actual_top  # 实际渲染高度
            else:
                # 整行或部分行模式：计算所有选中字符的边界
                min_top = float('inf')
                max_bottom = float('-inf')

                for char in selected_chars:
                    char_bbox = line_font.getbbox(char['char'])
                    char_top = char['y'] + char_bbox[1]
                    char_bottom = char['y'] + char_bbox[3]
                    min_top = min(min_top, char_top)
                    max_bottom = max(max_bottom, char_bottom)

                region_y = max_bottom  # create_mask_image期望的是底部位置
                region_height = max_bottom - min_top  # 实际渲染高度

            # 创建抹除区域（使用实际渲染边界）
            erasure_regions.append({
                'x': region_x,
                'y': region_y,  # 使用实际字符底部位置
                'width': region_width,
                'height': region_height,  # 使用实际渲染高度
                'chars': selected_chars,
                'line_idx': line_idx,
                'type': line_mode,
                'shape': 'straight'  # 与原脚本一致
            })

    return erasure_regions

def generate_colorblock_erasure_dataset(output_dir, num_samples, current_idx, font_paths, font_sizes,
                                       texture_paths=None, dpi=300, erasure_mode='random', expand_boundary=False,
                                       multi_line_erasure=True, left_right_expand_boundary_ratio=0.3,
                                       line_spacing=10, char_spacing=5, num_lines=None, chars_per_line=None,
                                       use_image_background=False, image_paths=None, text_colors=None,
                                       img_name=None, target_size=(1487, 2105), colorblock_info=None):
    """
    生成带彩色块的抹除数据集
    直接实现彩色块版本，确保文字在彩色块内部

    返回:
        bool: 成功返回True，失败返回False
    """
    try:
        # 创建输出目录
        for folder in ['im', 'mask', 'gt', 'show']:
            os.makedirs(os.path.join(output_dir, folder), exist_ok=True)

        # 确保行数与字体列表长度一致
        if num_lines is None:
            num_lines = len(font_paths)
        else:
            num_lines = min(num_lines, len(font_paths))

        # 加载所有字体
        fonts = []
        for i in range(num_lines):
            font_path = font_paths[i][1]
            font_size = font_sizes[i]
            font = load_font_from_tif(font_path, font_size)
            fonts.append([font_paths[i][0], font])

        # 生成样本
        if use_image_background and image_paths:
            # 使用图片背景
            try:
                # 随机选择一张图片
                image_path = random.choice(image_paths) if isinstance(image_paths, list) else image_paths
                if not os.path.exists(image_path):
                    print(f"警告: 图片路径不存在: {image_path}")
                    return False

                # 加载图片
                blank_image = Image.open(image_path)
                # 使用智能缩放
                blank_image = resize_image_with_aspect_ratio(blank_image, target_size)
                image_width, image_height = blank_image.size

                # 如果图片太小，跳过该样本
                if image_width < 100 or image_height < 100:
                    print(f"警告: 图片尺寸太小: {image_path}")
                    return False
            except Exception as e:
                print(f"警告: 加载图片失败: {image_path}, 错误: {e}")
                return False
        else:
            # 创建空白A4图像
            blank_image = create_blank_a4(dpi=dpi)
            blank_image = resize_image_with_aspect_ratio(blank_image, target_size)
            image_width, image_height = blank_image.size

        # 如果不是 RGB 模式，则转换为 RGB
        if blank_image.mode != 'RGB':
            blank_image = blank_image.convert('RGB')

        # 生成彩色块
        if not colorblock_info:
            print("警告: 缺少彩色块信息")
            return False

        # 获取当前图像尺寸
        current_width, current_height = blank_image.size

        # 彩色块坐标是基于目标尺寸生成的，需要缩放到当前图像尺寸
        scale_x = current_width / target_size[0]
        scale_y = current_height / target_size[1]
        # 使用较小的缩放比例来保持宽高比
        scale_factor = min(scale_x, scale_y)

        cb_x = int(colorblock_info['x'] * scale_x)
        cb_y = int(colorblock_info['y'] * scale_y)
        cb_width = int(colorblock_info['width'] * scale_x)
        cb_height = int(colorblock_info['height'] * scale_y)

        print(f"图像尺寸: {current_width}x{current_height}, 目标尺寸: {target_size}")
        print(f"缩放比例: {scale_x:.3f}x{scale_y:.3f}, 统一缩放: {scale_factor:.3f}")
        print(f"彩色块位置: ({cb_x},{cb_y}), 尺寸: {cb_width}x{cb_height}")

        # 先获取彩色块信息，这些变量在后面会用到
        cb_color = colorblock_info['color']
        shape_mask = colorblock_info['shape_mask']
        shape_type = colorblock_info.get('shape_type', 'rounded_rect')  # 获取形状类型，默认为圆角矩形
        use_tight_fit = colorblock_info.get('use_tight_fit', False)  # 获取边界策略，默认安全边距模式

        # 修复：不再重新计算字体大小，直接使用传入的字体和文字
        # 如果有预生成的字体，直接使用
        if colorblock_info and 'final_fonts' in colorblock_info:
            fonts = colorblock_info['final_fonts']
            print("使用预生成的最终字体对象")
        else:
            # 否则重新加载字体（保持原有逻辑作为备用）
            fonts = []
            for i in range(num_lines):
                font_path = font_paths[i][1]
                font_size = font_sizes[i]
                font = load_font_from_tif(font_path, font_size)
                fonts.append([font_paths[i][0], font])
            print("重新加载字体对象")

        # 调整形状mask大小
        scaled_shape_mask = shape_mask.resize((cb_width, cb_height), Image.LANCZOS)

        # 在背景图上添加彩色块
        original_image = blank_image.copy()
        colorblock_img = Image.new('RGB', (cb_width, cb_height), cb_color)

        # 确保彩色块不超出图像边界
        paste_x = max(0, min(cb_x, current_width - cb_width))
        paste_y = max(0, min(cb_y, current_height - cb_height))

        # 如果彩色块超出边界，需要裁剪
        if paste_x + cb_width > current_width:
            cb_width = current_width - paste_x
        if paste_y + cb_height > current_height:
            cb_height = current_height - paste_y

        if cb_width > 0 and cb_height > 0:
            colorblock_img = colorblock_img.resize((cb_width, cb_height), Image.LANCZOS)
            scaled_shape_mask = scaled_shape_mask.resize((cb_width, cb_height), Image.LANCZOS)
            original_image.paste(colorblock_img, (paste_x, paste_y), scaled_shape_mask)

            # 更新彩色块信息
            cb_x, cb_y = paste_x, paste_y
        else:
            print("警告: 彩色块尺寸无效，跳过该样本")
            return False

        # 修复：优先使用预生成的文字行，避免重复生成导致尺寸不匹配
        if colorblock_info and 'pregenerated_lines' in colorblock_info:
            # 使用预生成的文字行（已经与色块尺寸匹配）
            lines = colorblock_info['pregenerated_lines']
            print("✅ 使用预生成的文字行（与色块尺寸匹配）")
        else:
            # 备用方案：重新生成文本行
            lines = generate_text_lines(num_lines, chars_per_line, fonts)
            print("⚠️  重新生成文字行（可能与色块尺寸不匹配）")

        # 更新彩色块信息用于后续处理
        updated_colorblock_info = {
            'x': cb_x, 'y': cb_y, 'width': cb_width, 'height': cb_height,
            'color': cb_color, 'shape_mask': scaled_shape_mask
        }

        # 计算文本位置，根据边界策略调整安全边距
        positioned_lines = calculate_text_positions_in_colorblock(
            lines, cb_x, cb_y, cb_width, cb_height,
            line_spacing=line_spacing, char_spacing=char_spacing, shape_type=shape_type, use_tight_fit=use_tight_fit
        )

        if not positioned_lines:
            print("警告: 无法在彩色块内放置文字")
            return False

        # 字符边界检查已在位置计算时完成，无需额外验证

        # 在彩色块上渲染文字
        original_image = render_text(original_image, positioned_lines, None, text_colors=text_colors)
        if original_image is None:
            print("原图渲染文字出错")
            return False

        # 最终验证：检查所有文字是否都在色块内（使用正确的坐标系统）
        print(f"🔍 最终验证: 检查文字是否在色块内")
        all_chars_inside = True
        for line_idx, line in enumerate(positioned_lines):
            for char_idx, char in enumerate(line['chars']):
                # 使用字符的实际渲染边界（如果有的话）
                if 'actual_left' in char and 'actual_right' in char and 'actual_top' in char and 'actual_bottom' in char:
                    char_left = char['actual_left']
                    char_right = char['actual_right']
                    char_top = char['actual_top']
                    char_bottom = char['actual_bottom']
                else:
                    # 回退到基本计算（但这可能不准确）
                    print(f"⚠️  字符 '{char['char']}' 缺少实际边界信息，使用基本计算")
                    char_left = char['x']
                    char_right = char['x'] + char['width']
                    char_top = char['y'] - char['height']
                    char_bottom = char['y']

                # 检查字符是否在色块边界内（留5像素安全边距）
                safety_margin = 5
                cb_left = cb_x + safety_margin
                cb_right = cb_x + cb_width - safety_margin
                cb_top = cb_y + safety_margin
                cb_bottom = cb_y + cb_height - safety_margin

                if (char_left < cb_left or char_right > cb_right or
                    char_top < cb_top or char_bottom > cb_bottom):
                    print(f"❌ 字符 '{char['char']}' 超出色块边界!")
                    print(f"   字符实际渲染位置: ({char_left:.1f}, {char_top:.1f}) -> ({char_right:.1f}, {char_bottom:.1f})")
                    print(f"   色块安全边界: ({cb_left}, {cb_top}) -> ({cb_right}, {cb_bottom})")
                    all_chars_inside = False

        if all_chars_inside:
            print(f"✅ 验证通过: 所有文字都在色块内")
        else:
            print(f"❌ 验证失败: 有文字超出色块边界，跳过此样本")
            return False

        # 选择抹除区域（使用彩色块专用函数）
        erasure_regions = select_colorblock_erasure_regions(
            positioned_lines, erasure_mode, updated_colorblock_info,
            expand_boundary, multi_line_erasure, left_right_expand_boundary_ratio
        )

        # 调试信息：输出mask区域信息，验证修复效果
        if erasure_regions:
            print(f"生成了 {len(erasure_regions)} 个mask区域:")
            for i, region in enumerate(erasure_regions):
                print(f"  区域{i}: x={region['x']}, y={region['y']}, w={region['width']}, h={region['height']}")
                if region['chars']:
                    first_char = region['chars'][0]
                    print(f"    首字符: '{first_char['char']}' at ({first_char['x']}, {first_char['y']})")
        else:
            print("警告: 未生成任何mask区域")

        # 创建GT图像（不包含被抹除的文字，显示擦除后的彩色块效果）
        # 先创建只有彩色块的背景图像
        gt_base_image = blank_image.copy()
        if cb_width > 0 and cb_height > 0:
            colorblock_img = Image.new('RGB', (cb_width, cb_height), cb_color)
            gt_base_image.paste(colorblock_img, (cb_x, cb_y), scaled_shape_mask)

        # 在彩色块上渲染文字，但排除被抹除的字符（与原脚本逻辑一致）
        gt_image = render_text(gt_base_image, positioned_lines, None, erasure_regions, is_gt=True, text_colors=text_colors)
        if gt_image is None:
            print("GT图像渲染文字出错")
            return False

        # 创建掩码图像（基于字符轮廓，与原脚本一致）
        mask_image, _ = create_colorblock_mask_image((image_width, image_height), erasure_regions, original_image)

        # 调试信息：验证mask区域与字符位置的对齐（修复后）
        if erasure_regions:
            print(f"✅ 生成了 {len(erasure_regions)} 个mask区域（已修复坐标）:")
            for i, region in enumerate(erasure_regions):
                print(f"  区域{i}: x={region['x']}, y={region['y']}, w={region['width']}, h={region['height']}")
                if region['chars']:
                    first_char = region['chars'][0]
                    print(f"    首字符: '{first_char['char']}' 基线位置=({first_char['x']}, {first_char['y']})")

                    # 获取字符的bbox信息进行详细分析
                    if positioned_lines and 'font' in positioned_lines[0]:
                        font = positioned_lines[0]['font']
                        if font:
                            bbox = font.getbbox(first_char['char'])
                            actual_top = first_char['y'] + bbox[1]
                            actual_bottom = first_char['y'] + bbox[3]
                            print(f"    字符bbox: {bbox}")
                            print(f"    实际渲染范围: 顶部={actual_top}, 底部={actual_bottom}")
                            print(f"    mask覆盖范围: 顶部={region['y'] - region['height']}, 底部={region['y']}")
                            print(f"    ✅ 对齐检查: mask顶部应该≈实际顶部 ({region['y'] - region['height']} ≈ {actual_top})")

        print(f"GT图像处理完成，显示擦除后的彩色块效果")

        # 创建拼接图像用于可视化（使用彩色块专用函数）
        combined_image = create_colorblock_combined_image(original_image, gt_image, mask_image, positioned_lines, erasure_regions, updated_colorblock_info)

        # 保存图像
        if img_name:
            name = os.path.splitext(os.path.basename(image_path))[0]
            original_path = os.path.join(output_dir, 'im', name + "_" + str(current_idx) + ".png")
            mask_path = os.path.join(output_dir, 'mask', name + "_" + str(current_idx) + ".png")
            gt_path = os.path.join(output_dir, 'gt', name + "_" + str(current_idx) + ".png")
            show_path = os.path.join(output_dir, 'show', name + "_" + str(current_idx) + ".png")
        else:
            original_path = os.path.join(output_dir, 'im', f'sample_{current_idx:05d}.png')
            mask_path = os.path.join(output_dir, 'mask', f'sample_{current_idx:05d}.png')
            gt_path = os.path.join(output_dir, 'gt', f'sample_{current_idx:05d}.png')
            show_path = os.path.join(output_dir, 'show', f'sample_{current_idx:05d}.png')

        if os.path.exists(original_path):
            print(f"{original_path} 已存在，跳过")
            return False

        original_image.save(original_path)
        mask_image.save(mask_path)
        gt_image.save(gt_path)
        combined_image.save(show_path)

        return True

    except Exception as e:
        print(f"生成彩色块数据集时出错 (current_idx: {current_idx}): {e}")
        return False
    """并行生成样本，复用原脚本结构"""
    print(f"开始处理文件夹: {image_folder}")

def generate_samples_parallel(image_folder, output_folder, total_samples=10000, num_processes=4):
    """并行生成样本，图像索引共享，样本数量限制控制"""
    # 创建输出目录
    for folder in ['im', 'mask', 'gt', 'show']:
        os.makedirs(os.path.join(output_folder, folder), exist_ok=True)

    # 收集图片文件
    image_files = collect_image_files(image_folder)
    print(f"收集到 {len(image_files)} 个图片文件")

    if not image_files:
        print(f"警告：{image_folder} 中没有图片文件")
        return

    # 重要修复：限制处理的图片数量，确保不超过total_samples
    if len(image_files) > total_samples:
        print(f"⚠️ 警告：图片数量({len(image_files)})超过了指定的样本数量({total_samples})")
        print(f"🔍 正在随机选择{total_samples}张图片进行处理...")
        # 随机选择total_samples数量的图片
        image_files = random.sample(image_files, total_samples)
        print(f"✅ 已选择{len(image_files)}张图片")
    else:
        print(f"📊 图片数量({len(image_files)})不超过指定的样本数量({total_samples})，将处理所有图片")

    # 显示前几个图片文件路径用于调试
    print("前5个图片文件:")
    for i, img_path in enumerate(image_files[:5]):
        print(f"  {i+1}: {img_path}")

    # 初始化共享状态
    shared = init_shared_state(image_files)
    print(f"共享状态初始化完成，图片总数: {shared.total_images}")
    # 计算每个进程应生成的样本数，确保总数准确
    base_samples_per_process = total_samples // num_processes
    remainder = total_samples % num_processes

    print(f"样本分配: 总共{total_samples}个样本，{num_processes}个进程")
    print(f"基础分配: 每个进程{base_samples_per_process}个样本")
    if remainder > 0:
        print(f"余数分配: 前{remainder}个进程额外生成1个样本")

    # 创建进程池，为每个进程分配准确的样本数
    with Pool(processes=num_processes) as pool:
        args_list = []
        for i in range(num_processes):
            # 前remainder个进程多生成1个样本
            samples_for_this_process = base_samples_per_process + (1 if i < remainder else 0)
            args_list.append((output_folder, shared, samples_for_this_process, i))
            print(f"进程{i}: 分配{samples_for_this_process}个样本")

        pool.map(generate_samples_task, args_list)
        # 等待所有进程完成
        pool.close()
        pool.join()

    # 输出统计信息
    actual_generated = shared.actual_generated
    print(f"{image_folder} 处理完成")
    print(f"预分配样本数: {total_samples}")
    print(f"实际生成样本数: {actual_generated}")
    if actual_generated < total_samples:
        print(f"⚠️  有 {total_samples - actual_generated} 个样本生成失败被跳过")
    print(f"图片使用统计：")
    print(f"- 顺序使用图片数量: {min(shared.image_index, shared.total_images)}")
    print(f"- 随机重复使用次数: {max(0, total_samples - shared.total_images)}")

    return actual_generated  # 返回实际生成的数量

def check_paths():
    """检查必要的路径是否存在"""
    # 检查字体路径
    if not os.path.exists(FONT_PATH):
        print(f"❌ 错误：字体文件夹 {FONT_PATH} 不存在")
        print("请确保字体文件夹存在，或修改 FONT_PATH 配置")
        return False

    # 检查图片路径
    if not os.path.exists(IMAGE_PATH):
        print(f"❌ 错误：背景图片文件夹 {IMAGE_PATH} 不存在")
        print("请确保背景图片文件夹存在，或修改 IMAGE_PATH 配置")
        return False

    # 创建输出目录
    try:
        os.makedirs(OUTPUT_PATH, exist_ok=True)
        # 创建输出子目录
        for folder in ['im', 'mask', 'gt', 'show']:
            os.makedirs(os.path.join(OUTPUT_PATH, folder), exist_ok=True)
        print(f"✅ 输出目录已创建：{OUTPUT_PATH}")
    except Exception as e:
        print(f"❌ 错误：无法创建输出目录 {OUTPUT_PATH}: {e}")
        return False

    return True

def main():
    """主函数，复用原脚本结构"""
    print("=" * 60)
    print("自然场景+彩色块文字消除数据集生成脚本")
    print("=" * 60)

    # 检查路径配置
    if not check_paths():
        print("请检查路径配置后重新运行脚本")
        return

    # 检查图片子文件夹
    image_subfolders = []
    if os.path.isdir(IMAGE_PATH):
        # 如果IMAGE_PATH直接包含图片文件，直接使用
        image_files = [f for f in os.listdir(IMAGE_PATH)
                      if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
        if image_files:
            image_subfolders = ['.']  # 使用当前目录
        else:
            # 查找子文件夹
            for item in os.listdir(IMAGE_PATH):
                item_path = os.path.join(IMAGE_PATH, item)
                if os.path.isdir(item_path):
                    # 检查子文件夹是否包含图片
                    sub_images = [f for f in os.listdir(item_path)
                                 if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
                    if sub_images:
                        image_subfolders.append(item)

    if not image_subfolders:
        print(f"❌ 错误：在 {IMAGE_PATH} 中未找到任何图片文件")
        return

    print(f"发现图片文件夹：{image_subfolders}")

    # 🔥 关键修复：限制总样本数量，而不是每个文件夹都生成SAMPLES_PER_FOLDER个
    total_folders = len(image_subfolders)
    if total_folders > 1:
        print(f"⚠️  发现{total_folders}个图片文件夹，将平均分配样本数量")
        samples_per_folder = max(1, SAMPLES_PER_FOLDER // total_folders)
        remaining_samples = SAMPLES_PER_FOLDER % total_folders
        print(f"每个文件夹基础分配: {samples_per_folder}个样本")
        if remaining_samples > 0:
            print(f"前{remaining_samples}个文件夹额外分配1个样本")
    else:
        samples_per_folder = SAMPLES_PER_FOLDER
        remaining_samples = 0

    total_generated = 0

    # 遍历图片文件夹
    for folder_idx, image_subfolder in enumerate(image_subfolders):
        if image_subfolder == '.':
            input_path = IMAGE_PATH
            print(f"开始处理文件夹：{input_path}")
        else:
            input_path = os.path.join(IMAGE_PATH, image_subfolder)
            print(f"开始处理文件夹：{input_path}")

        # 计算这个文件夹应该生成的样本数
        if total_folders > 1:
            folder_samples = samples_per_folder + (1 if folder_idx < remaining_samples else 0)
            print(f"文件夹 {folder_idx + 1}/{total_folders}: 分配{folder_samples}个样本")
        else:
            folder_samples = SAMPLES_PER_FOLDER

        actual_generated = generate_samples_parallel(
            image_folder=input_path,
            output_folder=OUTPUT_PATH,
            total_samples=folder_samples,
            num_processes=6
        )

        total_generated += actual_generated
        print(f"文件夹处理完成，累计生成: {total_generated}/{SAMPLES_PER_FOLDER} 个样本")

    # 🔥 补丁：如果生成数量不足，继续生成直到达到目标
    max_retry_rounds = 3  # 最多重试3轮，避免无限循环
    retry_round = 0

    while total_generated < SAMPLES_PER_FOLDER and retry_round < max_retry_rounds:
        retry_round += 1
        shortage = SAMPLES_PER_FOLDER - total_generated
        print("=" * 60)
        print(f"🔄 第{retry_round}轮补充生成")
        print(f"当前已生成: {total_generated}/{SAMPLES_PER_FOLDER}")
        print(f"需要补充: {shortage} 个样本")
        print("=" * 60)

        # 重新分配补充任务到各个文件夹
        if total_folders > 1:
            base_shortage_per_folder = max(1, shortage // total_folders)
            remaining_shortage = shortage % total_folders
            print(f"每个文件夹基础补充: {base_shortage_per_folder}个样本")
            if remaining_shortage > 0:
                print(f"前{remaining_shortage}个文件夹额外补充1个样本")
        else:
            base_shortage_per_folder = shortage
            remaining_shortage = 0

        # 为每个文件夹补充生成样本
        for folder_idx, image_subfolder in enumerate(image_subfolders):
            if image_subfolder == '.':
                input_path = IMAGE_PATH
            else:
                input_path = os.path.join(IMAGE_PATH, image_subfolder)

            # 计算这个文件夹需要补充的样本数
            if total_folders > 1:
                folder_shortage = base_shortage_per_folder + (1 if folder_idx < remaining_shortage else 0)
                if folder_shortage <= 0:
                    continue
                print(f"文件夹 {folder_idx + 1}/{total_folders}: 补充{folder_shortage}个样本")
            else:
                folder_shortage = shortage

            additional_generated = generate_samples_parallel(
                image_folder=input_path,
                output_folder=OUTPUT_PATH,
                total_samples=folder_shortage,
                num_processes=6
            )

            total_generated += additional_generated
            print(f"文件夹补充完成，累计生成: {total_generated}/{SAMPLES_PER_FOLDER} 个样本")

            # 如果已经达到目标，提前退出
            if total_generated >= SAMPLES_PER_FOLDER:
                break

    print("=" * 60)
    print("数据集生成完成！")
    print(f"总共生成样本数: {total_generated}")
    print(f"目标样本数: {SAMPLES_PER_FOLDER}")
    if total_generated >= SAMPLES_PER_FOLDER:
        print("✅ 成功达到目标样本数量")
    else:
        print(f"⚠️  未能达到目标数量，可能由于样本生成失败率过高")
        print(f"建议：检查字体文件、图片质量或调整生成参数")
    print(f"输出目录：{OUTPUT_PATH}")
    print("=" * 60)

if __name__ == "__main__":
    main()
