#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试mask偏移修复效果
"""

import os
import sys

# 添加项目根目录到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
sys.path.append(project_root)

def test_single_sample():
    """测试生成单个样本"""
    from nature_colorblock_generate_script import generate_samples_task, init_shared_state, collect_image_files
    
    # 设置测试参数
    output_folder = os.path.join(script_dir, "test_output")
    os.makedirs(output_folder, exist_ok=True)
    
    # 创建测试用的图片列表（使用一个简单的测试图片）
    test_image_path = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/nature/"
    
    if not os.path.exists(test_image_path):
        print(f"测试图片路径不存在: {test_image_path}")
        print("请修改test_image_path为有效的图片路径")
        return False
    
    # 收集图片文件
    image_files = collect_image_files(test_image_path)
    if not image_files:
        print(f"在 {test_image_path} 中未找到图片文件")
        return False
    
    print(f"找到 {len(image_files)} 个图片文件")
    
    # 初始化共享状态
    shared = init_shared_state(image_files)
    
    # 生成1个测试样本
    try:
        generate_samples_task((output_folder, shared, 1, 0))
        print("✅ 测试样本生成成功")
        print(f"请检查输出目录: {output_folder}")
        print("特别查看 show 文件夹中的对比图像，验证mask是否正确对齐")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("测试彩色块mask偏移修复效果")
    print("=" * 60)
    
    success = test_single_sample()
    
    if success:
        print("\n✅ 测试完成！请检查生成的图像验证修复效果。")
    else:
        print("\n❌ 测试失败！请检查错误信息。")
