#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的彩色块脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append('.')

def test_import():
    """测试脚本是否可以正常导入"""
    try:
        # 尝试导入脚本中的函数
        from examples.inpainting.nature_colorblock_full_inpainting_generate_script import (
            generate_colorblock_color,
            generate_colorblock_for_text,
            create_colorblock_mask_image,
            select_colorblock_erasure_regions
        )
        print("✅ 脚本导入成功")
        return True
    except ImportError as e:
        print(f"❌ 脚本导入失败: {e}")
        return False
    except SyntaxError as e:
        print(f"❌ 脚本语法错误: {e}")
        return False

def test_colorblock_functions():
    """测试彩色块相关函数"""
    try:
        from examples.inpainting.nature_colorblock_full_inpainting_generate_script import (
            generate_colorblock_color,
            generate_colorblock_for_text
        )
        
        # 测试颜色生成
        color = generate_colorblock_color()
        print(f"✅ 生成彩色块颜色: {color}")
        
        # 测试色块生成
        cb_width, cb_height, cb_x, cb_y, shape_mask = generate_colorblock_for_text(
            text_width=100, text_height=50, shape_type='ellipse', 
            use_tight_fit=False, image_size=(800, 600)
        )
        print(f"✅ 生成色块: 位置({cb_x}, {cb_y}), 尺寸{cb_width}x{cb_height}")
        
        return True
    except Exception as e:
        print(f"❌ 函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("测试修改后的彩色块脚本")
    print("=" * 50)
    
    # 测试导入
    if not test_import():
        return False
    
    # 测试函数
    if not test_colorblock_functions():
        return False
    
    print("=" * 50)
    print("✅ 所有测试通过！脚本修改成功")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
