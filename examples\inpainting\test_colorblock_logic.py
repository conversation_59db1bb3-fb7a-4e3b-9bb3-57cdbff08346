#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试彩色块逻辑的简单脚本
"""

import sys
import os
import random

# 添加项目根目录到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
sys.path.append(project_root)

from examples.inpainting.nature_colorblock_generate_script import (
    calculate_dynamic_font_sizes,
    generate_colorblock_shape,
    COLORBLOCK_AREA_RATIO_RANGE,
    COLORBLOCK_SHAPE_PROB
)

def test_colorblock_logic():
    """测试彩色块与文字大小匹配逻辑"""
    print("=" * 60)
    print("测试彩色块与文字大小匹配逻辑")
    print("=" * 60)
    
    # 模拟图像尺寸
    image_size = (1487, 2105)
    num_lines = 4
    
    # 测试多个不同的色块大小
    for i in range(5):
        print(f"\n--- 测试 {i+1} ---")
        
        # 生成彩色块
        area_ratio = random.uniform(*COLORBLOCK_AREA_RATIO_RANGE)
        shape_type = random.choices(
            list(COLORBLOCK_SHAPE_PROB.keys()),
            weights=list(COLORBLOCK_SHAPE_PROB.values())
        )[0]
        
        cb_x, cb_y, cb_width, cb_height, shape_mask = generate_colorblock_shape(
            image_size, area_ratio, shape_type
        )
        
        print(f"图像尺寸: {image_size}")
        print(f"色块面积比例: {area_ratio:.3f}")
        print(f"色块形状: {shape_type}")
        print(f"色块位置: ({cb_x}, {cb_y})")
        print(f"色块尺寸: {cb_width} x {cb_height}")
        
        # 计算字体大小
        font_sizes = calculate_dynamic_font_sizes(image_size, num_lines, (cb_width, cb_height))
        
        print(f"生成的字体大小: {font_sizes}")
        print(f"平均字体大小: {sum(font_sizes)/len(font_sizes):.1f}")
        
        # 计算字符数
        margin_ratio = 0.1
        available_width = cb_width * (1 - 2 * margin_ratio)
        avg_font_size = sum(font_sizes) / len(font_sizes)
        estimated_char_width = avg_font_size * 0.8
        char_spacing = 5
        max_chars_per_line = max(2, int(available_width / (estimated_char_width + char_spacing)))
        chars_per_line = min(max_chars_per_line, random.randint(3, 8))
        
        print(f"可用宽度: {available_width:.1f}")
        print(f"估算字符宽度: {estimated_char_width:.1f}")
        print(f"最大字符数: {max_chars_per_line}")
        print(f"实际字符数: {chars_per_line}")
        
        # 验证文字是否能放入色块
        total_text_width = chars_per_line * estimated_char_width + (chars_per_line - 1) * char_spacing
        total_text_height = num_lines * avg_font_size * 1.2 + (num_lines - 1) * 20  # 行间距20
        
        print(f"预估文字总宽度: {total_text_width:.1f} (可用: {available_width:.1f})")
        print(f"预估文字总高度: {total_text_height:.1f} (可用: {cb_height * 0.8:.1f})")
        
        width_fit = total_text_width <= available_width
        height_fit = total_text_height <= cb_height * 0.8
        
        print(f"宽度匹配: {'✅' if width_fit else '❌'}")
        print(f"高度匹配: {'✅' if height_fit else '❌'}")
        print(f"整体匹配: {'✅' if width_fit and height_fit else '❌'}")

if __name__ == "__main__":
    test_colorblock_logic()
