#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试彩色块生成脚本的功能
"""

import os
import sys
from PIL import Image, ImageDraw
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from nature_colorblock_generate_script import (
    generate_colorblock_color,
    calculate_brightness,
    generate_contrast_text_color,
    choose_size_relation,
    generate_colorblock_shape,
    generate_text_colors,
    calculate_dynamic_font_sizes,
    choose_mask_strategy,
    FONT_SIZE_RATIO_RANGE,
    MASK_STRATEGY_PROB
)

def test_colorblock_color_generation():
    """测试彩色块颜色生成"""
    print("测试彩色块颜色生成...")
    
    colors = []
    for i in range(10):
        color = generate_colorblock_color()
        brightness = calculate_brightness(color)
        colors.append((color, brightness))
        print(f"颜色 {i+1}: RGB{color}, 亮度: {brightness:.3f}")
    
    print("✓ 彩色块颜色生成测试完成\n")
    return colors

def test_contrast_text_color():
    """测试对比文字颜色生成"""
    print("测试对比文字颜色生成...")
    
    test_bg_colors = [
        (255, 255, 255),  # 白色背景
        (0, 0, 0),        # 黑色背景
        (255, 0, 0),      # 红色背景
        (0, 255, 0),      # 绿色背景
        (128, 128, 128),  # 灰色背景
    ]
    
    for bg_color in test_bg_colors:
        text_color = generate_contrast_text_color(bg_color)
        bg_brightness = calculate_brightness(bg_color)
        text_brightness = calculate_brightness(text_color)
        
        print(f"背景色: RGB{bg_color} (亮度: {bg_brightness:.3f}) -> "
              f"文字色: RGB{text_color} (亮度: {text_brightness:.3f})")
    
    print("✓ 对比文字颜色生成测试完成\n")

def test_size_relation():
    """测试尺寸关系选择"""
    print("测试尺寸关系选择...")
    
    relations = {}
    for i in range(100):
        relation_type, ratio = choose_size_relation()
        if relation_type not in relations:
            relations[relation_type] = []
        relations[relation_type].append(ratio)
    
    for relation_type, ratios in relations.items():
        avg_ratio = sum(ratios) / len(ratios)
        print(f"{relation_type}: {len(ratios)}次, 平均比例: {avg_ratio:.2f}, "
              f"范围: {min(ratios):.2f}-{max(ratios):.2f}")
    
    print("✓ 尺寸关系选择测试完成\n")

def test_colorblock_shape_generation():
    """测试彩色块形状生成"""
    print("测试彩色块形状生成...")
    
    image_size = (800, 600)
    area_ratio = 0.15
    
    shapes = ['rounded_rect', 'ellipse', 'polygon']
    
    for shape_type in shapes:
        x, y, width, height, shape_mask = generate_colorblock_shape(
            image_size, area_ratio, shape_type
        )
        
        actual_area = np.sum(np.array(shape_mask) > 128)
        expected_area = image_size[0] * image_size[1] * area_ratio
        
        print(f"{shape_type}: 位置({x}, {y}), 尺寸({width}x{height}), "
              f"实际面积: {actual_area}, 期望面积: {expected_area:.0f}")
    
    print("✓ 彩色块形状生成测试完成\n")

def test_text_color_generation():
    """测试文字颜色生成"""
    print("测试文字颜色生成...")
    
    colorblock_color = (100, 150, 200)
    num_lines = 5
    
    for i in range(3):
        text_colors = generate_text_colors(num_lines, colorblock_color)
        white_count = sum(1 for color in text_colors if color == (255, 255, 255))
        colored_count = len(text_colors) - white_count
        
        print(f"测试 {i+1}: 白色文字 {white_count}个, 彩色文字 {colored_count}个")
        for j, color in enumerate(text_colors):
            if color != (255, 255, 255):
                print(f"  行 {j+1}: RGB{color}")
    
    print("✓ 文字颜色生成测试完成\n")

def test_dynamic_font_sizes():
    """测试动态字号计算"""
    print("测试动态字号计算...")

    test_image_sizes = [
        (800, 600),    # 短边600
        (1024, 768),   # 短边768
        (1920, 1080),  # 短边1080
        (2480, 3508),  # 短边2480
    ]

    for image_size in test_image_sizes:
        short_edge = min(image_size)
        num_lines = 5
        font_sizes = calculate_dynamic_font_sizes(image_size, num_lines)

        min_expected = int(short_edge * FONT_SIZE_RATIO_RANGE[0])
        max_expected = int(short_edge * FONT_SIZE_RATIO_RANGE[1])

        print(f"图片尺寸: {image_size}, 短边: {short_edge}")
        print(f"期望字号范围: {min_expected}-{max_expected}")
        print(f"实际字号: {font_sizes}")
        print(f"字号占短边比例: {[f/short_edge*100:.1f}% " for f in font_sizes]}")
        print()

    print("✓ 动态字号计算测试完成\n")

def test_mask_strategy_selection():
    """测试Mask策略选择"""
    print("测试Mask策略选择...")

    strategies = {}
    for i in range(1000):
        strategy = choose_mask_strategy()
        if strategy not in strategies:
            strategies[strategy] = 0
        strategies[strategy] += 1

    print("Mask策略分布（1000次测试）:")
    for strategy, count in strategies.items():
        expected_prob = MASK_STRATEGY_PROB.get(strategy, 0)
        actual_prob = count / 1000
        print(f"{strategy}: {count}次 ({actual_prob:.1%}), 期望: {expected_prob:.1%}")

    print("✓ Mask策略选择测试完成\n")

def create_visual_test():
    """创建可视化测试图像"""
    print("创建可视化测试图像...")
    
    # 创建测试图像
    img_size = (800, 600)
    test_img = Image.new('RGB', img_size, (50, 100, 150))  # 深蓝色背景
    
    # 生成彩色块
    area_ratio = 0.2
    shape_type = 'rounded_rect'
    
    x, y, width, height, shape_mask = generate_colorblock_shape(
        img_size, area_ratio, shape_type
    )
    
    # 生成彩色块颜色
    colorblock_color = generate_colorblock_color()
    
    # 创建彩色块
    colorblock_img = Image.new('RGB', (width, height), colorblock_color)
    
    # 合成到背景上
    test_img.paste(colorblock_img, (x, y), shape_mask)
    
    # 保存测试图像
    output_dir = "test_output"
    os.makedirs(output_dir, exist_ok=True)
    test_img.save(os.path.join(output_dir, "colorblock_test.png"))
    
    print(f"✓ 可视化测试图像已保存到 {output_dir}/colorblock_test.png")
    print(f"  彩色块颜色: RGB{colorblock_color}")
    print(f"  彩色块位置: ({x}, {y}), 尺寸: {width}x{height}")

def main():
    """运行所有测试"""
    print("=" * 50)
    print("彩色块生成脚本功能测试")
    print("=" * 50)
    
    try:
        test_colorblock_color_generation()
        test_contrast_text_color()
        test_size_relation()
        test_colorblock_shape_generation()
        test_text_color_generation()
        test_dynamic_font_sizes()
        test_mask_strategy_selection()
        create_visual_test()
        
        print("=" * 50)
        print("✅ 所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
