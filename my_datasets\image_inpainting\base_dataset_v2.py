#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/25 17:46
# <AUTHOR> <EMAIL>
# @FileName: base_dataset_v1

import os
import json
import random
import shutil
import uuid
import warnings
from enum import Enum


import numpy as np
import torch
from PIL import Image
from torch.utils.data import Dataset
from torchvision import transforms
from modules.utils.log import LOGGER
from modules.utils.torch_utils import set_seed
from modules.utils.image_utils import (
    resize_image_pair,
    random_crop_max_square_pair,
    default_degrade_image_quality,
    random_crop_local_square_pair_with_resolution,
    random_crop_local_square_pair_with_resolution_padding,
    resize_image_pair_padding,
    random_crop_max_square_pair_padding,
    denormalize_tensor_image_rgb,
    denormalize_mask,
)
from modules.doc_degradation.core.degradation_pipe import DegradationPipe, StrategyScheduler
import cv2

Image.MAX_IMAGE_PIXELS = None

# Suppress the specific warning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message="Palette images with Transparency expressed in bytes should be converted to RGBA images"
)

# import pudb
def HSV(img,h_adjust,s_adjust,v_adjust):
    '''
    默认：1 亮度不变
    h:调整色相
    s:调整饱和度
    v:调整亮度
    '''
    # 确保图像是RGB模式
    if img.mode != 'RGB':
        img = img.convert('RGB')

    # 转换为HSV色彩空间
    img_hsv = img.convert('HSV')

    # 分离HSV通道
    h, s, v = img_hsv.split()

    # 调整色相 (H通道)
    if h_adjust != 0:
        h_array = np.array(h).astype(np.int16)
        # 色相是循环的，范围是0-255
        h_array = (h_array + int(h_adjust * 255)) % 256
        h = Image.fromarray(h_array.astype(np.uint8))

    # 调整饱和度 (S通道)
    if s_adjust != 1.0:
        s_array = np.array(s).astype(np.float32)
        s_array = np.clip(s_array.astype(np.float32) * s_adjust, 0, 255).astype(np.uint8)
        s = Image.fromarray(s_array)

    # 调整亮度 (V通道)
    if v_adjust != 1.0:
        v_array = np.array(v).astype(np.float32)
        v_array = np.clip(v_array.astype(np.float32) * v_adjust, 0, 255).astype(np.uint8)
        v = Image.fromarray(v_array)

    # 合并通道
    img_enhanced = Image.merge('HSV', (h, s, v))

    # 转回RGB
    img_enhanced = img_enhanced.convert('RGB')

    return img_enhanced
def random_hsv_pair(image_list):
    '''
    :image_list: 图像对
    '''
    im_image=image_list[0]
    mask_image=image_list[1]
    gt_image=image_list[2]
    # h_adjust=random.uniform(1,1)# 改变色相
    h_adjust=random.uniform(0.9,1.1) # 改变色相
    s_adjust=random.uniform(0.9,1.1) # 调整饱和度
    v_adjust=random.uniform(0.9,1.1) # 调整亮度
    im_image_res=HSV(im_image,h_adjust,s_adjust,v_adjust)
    gt_image_res=HSV(gt_image,h_adjust,s_adjust,v_adjust)
    return [im_image_res,mask_image,gt_image_res]



def apply_mask_pil(image_pil, mask_pil):
    """
    Applies a mask to a PIL image using the formula: image * (1 - mask).
    
    Args:
        image_pil: Original PIL image (RGB or RGBA).
        mask_pil: PIL mask image (grayscale "L" mode) where 0=transparent, 255=opaque.
    
    Returns:
        PIL Image: The masked result where masked areas are transparent.
    """
    """
    使用PIL图像实现 image * (1 - mask)

    Args:
        image_pil: PIL格式的原始图像
        mask_pil: PIL格式的掩码图像，应为"L"模式(灰度图)，值范围0-255

    Returns:
        PIL格式的掩码应用后的图像
    """
    # 确保掩码是灰度图
    if mask_pil.mode != "L":
        mask_pil = mask_pil.convert("L")

    # 转换为NumPy数组
    image_np = np.array(image_pil).astype(np.float32) / 255.0
    mask_np = np.array(mask_pil).astype(np.float32) / 255.0

    # 扩展掩码维度以匹配图像通道
    if len(image_np.shape) == 3 and len(mask_np.shape) == 2:
        mask_np = mask_np[:, :, np.newaxis]

    # 应用掩码: image * (1 - mask)
    inverted_mask = 1.0 - mask_np
    result_np = image_np * inverted_mask

    # 转换回0-255范围
    result_np = (result_np * 255.0).astype(np.uint8)

    # 转换回PIL图像
    result_pil = Image.fromarray(result_np)

    return result_pil


class MyCustomDataset(Dataset):
    def __init__(self, data_root, jsonal_dataset_dir, mode='train', seed=-1, debug=False,kernel_size=(5,5)):
        """
        初始化数据集。
        :param data_root: 数据目录。
        :param mode: 模式 (train, val, test)。
        :param seed: 随机种子。
        """
        self.mode=mode
        if seed == -1:
            seed = random.randint(1, 12580)
        set_seed(seed)
        self.kernel_size=kernel_size
        # self.image_degrade_pipe = DegradationPipe(StrategyScheduler(config_path=None))

        assert data_root, "必须指定训练数据目录"
        self.data_root = data_root
        if debug:
            jsonl_data_path = os.path.join(jsonal_dataset_dir, f"{mode}_debug.jsonl") #data_root
        else:
            jsonl_data_path = os.path.join(jsonal_dataset_dir, f"{mode}_crop.jsonl") #data_root
        print(f"已加载jsonal文件：{jsonl_data_path}")
        LOGGER.info(f"加载数据元信息：{jsonl_data_path}")
        with open(jsonl_data_path, "r") as f:
            all_samples = [json.loads(line.strip()) for line in f if line.strip()]
        random.shuffle(all_samples)
        self.dataset = all_samples
        # 统计数据集分布数量
        stat = dict()
        for data in self.dataset:
            data_name = data["dataset_name"]
            if data_name in stat:
                stat[data_name] = stat[data_name] + 1
            else:
                stat[data_name] = 1
        for data_name, num in stat.items():
            LOGGER.info(f"数据统计, {data_name}: {num}")
        LOGGER.info(f"数据加载完毕, 共计{len(all_samples)}对样本")

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        """
        返回源图像和目标图像的路径及元信息。
        """
        data = self.dataset[idx]
        data_name = data["dataset_name"]
        im_image_path = os.path.join(self.data_root, data_name, data['im'])
        mask_image_path = os.path.join(self.data_root, data_name, data['mask'])
        gt_image_path = os.path.join(self.data_root, data_name, data['gt'])
        img_name=os.path.basename(im_image_path)# 获取文件名 # 方便调试
        # 加载源图像和目标图像
        im_image = Image.open(im_image_path).convert("RGB")
        mask_image = Image.open(mask_image_path).convert("L")
        gt_image=None
        if os.path.exists(gt_image_path):
            gt_image = Image.open(gt_image_path).convert("RGB")
        # mask_image = np.array(mask_image)
        # mask_image = cv2.dilate(mask_image, (self.kernel_size[0],self.kernel_size[1]), iterations=1)
        # mask_image = cv2.GaussianBlur(mask_image, self.kernel_size,0.8,)
        # mask_image = Image.fromarray(mask_image)
        # equal_img(im_image, gt_image, mask_image,print_str='裁切之前')
        crop_index=data['crop_index']
        # 对所有图片进行裁切
        im_image=im_image.crop(crop_index)
        mask_image=mask_image.crop(crop_index)
        if os.path.exists(gt_image_path):
            # 加载crop裁切区域
            gt_image=gt_image.crop(crop_index)
        else:
            width, height = mask_image.size
            gt_image = Image.new("RGB", (width, height), color=(0, 0, 0))  # 全黑图像
        # res_equal=equal_img(im_image, gt_image, mask_image,print_str='裁切之后')
        # allow_hsv_data_name = ["doc_ch_en1_filter_enhance", "doc_ch_en1_filter_uvdoc"
        #                        'table_enhance', 'table_uvdoc', 'nature_dataset_enhance'
        #                        ]
        # # 加入颜色增强方式 概率30%
        # if self.mode == 'train' and data_name in allow_hsv_data_name and random.random() < 0.5:
        #     im_image, mask_image, gt_image, = random_hsv_pair([im_image, mask_image, gt_image])
        # 消除区域直接通过mask的形式和原图相乘 将mask区域原图部分扣除
        # im_image=apply_mask_pil(im_image,mask_image)
        return {
            "im":im_image,
            "mask":mask_image,
            "gt":gt_image,
            'img_mess':f'{data_name}/{img_name}',
            "imagepath": os.path.basename(im_image_path)
        }


class ImageProcessingMethod(Enum):
    RESIZE_IMAGE_PAIR = {
        "func": resize_image_pair,
        "weight": 0.2,
        "flag": 0,
    }
    RANDOM_CROP_MAX_SQUARE_PAIR = {
        "func": random_crop_max_square_pair,
        "weight": 0.3,
        "flag": 1,
    }
    # 增加随机亮度调整

    # RANDOM_CROP_LOCAL_SQUARE_PAIR = {
    #     "func": random_crop_local_square_pair_with_resolution,
    #     "weight": 0.1,
    #     "flag": 2,
    # }
    # RESIZE_IMAGE_PAIR_PADDING = { # 要使用有bug 输出都是rgb模式 mask是L模式
    #     "func": resize_image_pair_padding,
    #     "weight": 0.2,
    #     "flag": 3,
    # }
    # RANDOM_CROP_MAX_SQUARE_PAIR_PADDING = {# 要使用有bug 输出都是rgb模式 mask是L模式
    #     "func": random_crop_max_square_pair_padding,
    #     "weight": 0.3,
    #     "flag": 4,
    # }
    # RANDOM_CROP_LOCAL_SQUARE_PAIR_PADDING = {
    #     "func": random_crop_local_square_pair_with_resolution_padding,
    #     "weight": 0.1,
    #     "flag": 5,
    # }

    @staticmethod
    def get_random_method():
        """
        随机选择一个图像预处理方法。
        :return: 返回 (函数, 标志值)
        """
        # 提取所有成员的权重
        methods = list(ImageProcessingMethod)
        weights = [method.value["weight"] for method in methods]

        # 按权重随机选择一个方法
        selected_method = random.choices(methods, weights, k=1)[0]

        # 获取对应的函数和标志值
        return selected_method.value["func"], selected_method.value["flag"]


def dynamic_collate_fn(batch, resolution_fun):
    """
    自定义的 collate_fn，用于动态调整分辨率。
    :param batch: 一个批次的数据，列表形式。
    :param resolution_fun: 一个函数，用于动态返回分辨率。
    :return: 返回动态调整分辨率后的批次数据。
    """
    # 动态获取分辨率
    resolution = resolution_fun()
    transform = transforms.Compose([transforms.ToTensor()])

    # 随机选择一个图像处理方法及其标志
    img_proc_func, img_proc_flag = ImageProcessingMethod.get_random_method()

    if img_proc_flag == 1 or img_proc_flag == 4:
        resolution = random.choice([768, 896, 1024])

    im_images = []
    mask_images = []
    gt_images = []
    resolutions = []
    img_proc_flags = []
    image_paths = []
    for item in batch:
        # 在inpainting 是三元组

        im_image = item["im"]
        mask_image = item["mask"]
        gt_image = item["gt"]
        image_path = item["imagepath"]
        # 应用选定的图像处理方法
        im_image, mask_image, gt_image= img_proc_func(
            image_list=[im_image, mask_image,gt_image],
            size=resolution, sampling=Image.LANCZOS,
        )
        # 通过图像处理可能会导致mask的模式改变
        if mask_image.mode=='RGB':
            mask_image=mask_image.convert('L')

        # 转为张量
        im_images.append(transform(im_image))
        mask_images.append(transform(mask_image))
        gt_images.append(transform(gt_image))
        resolutions.append(resolution)
        img_proc_flags.append(img_proc_flag)
        image_paths.append(image_path)

    # 将图像数据拼接成 Tensor 格式
    im_images = torch.stack(im_images)  # 拼接成 (batch_size, C, H, W)
    mask_images = torch.stack(mask_images)  # 拼接成 (batch_size, C, H, W)
    gt_images = torch.stack(gt_images)  # 拼接成 (batch_size, C, H, W)

    # 将分辨率和图像处理标志转为 Tensor 格式
    resolutions = torch.tensor(resolutions, dtype=torch.int32)        # 分辨率
    img_proc_flags = torch.tensor(img_proc_flags, dtype=torch.int32)  # 图像处理方法标志


    # 返回批次
    return {
        "im":im_images,
        "mask":mask_images,
        "gt":gt_images,
        "resolution": resolutions,
        "img_proc_flag": img_proc_flags,
        "imagepath": image_paths,
    }


def walk_dataloaders(loaders, weights=None):
    """
    遍历多个数据加载器，以随机顺序返回每个加载器的批次。
    每个加载器可以根据权重比例进行动态调整。
    :param loaders: 多个数据加载器的列表，每个元素是 (标志, DataLoader)。
    :param weights: 各个加载器的采样权重，列表或 None。如果为 None，则根据加载器数据长度自动计算。
    :yield: 返回 (标志, 批次)。
    """
    # 生成加载器的迭代器
    doing = [(flag, iter(loader), len(loader)) for flag, loader in loaders]

    # 如果未指定权重，根据数据加载器的长度计算权重
    if weights is None:
        total_length = sum(length for _, _, length in doing)
        weights = [length / total_length for _, _, length in doing]
    else:
        # 确保权重总和为 1
        weight_sum = sum(weights)
        weights = [w / weight_sum for w in weights]

    print()
    weight_with_flags = [(flag, weight) for (flag, _, _), weight in zip(doing, weights)]
    LOGGER.info(f"walk dataloader by weights: {weight_with_flags}")

    # 确保权重的数量与加载器数量一致
    assert len(weights) == len(doing), "权重数量必须与加载器数量一致"

    while doing:
        # 按权重随机选择一个加载器
        selected_idx = random.choices(range(len(doing)), weights=weights, k=1)[0]
        flag, it, length = doing[selected_idx]

        try:
            batch = next(it)
            yield flag, batch
        except StopIteration:
            # 当前加载器完成，移除
            del doing[selected_idx]
            del weights[selected_idx]

            # 重新计算权重
            if doing:
                total_length = sum(length for _, _, length in doing)
                weights = [length / total_length for _, _, length in doing]


def horizontal_concat_images(images):

    # 确保所有图片高度相同（可选：可以调整为最小高度或最大高度）
    heights = [img.height for img in images]
    min_height = min(heights)

    # 调整所有图片到相同高度（如果需要）
    resized_images = []
    for img in images:
        if img.height != min_height:
            # 保持宽高比进行缩放
            ratio = min_height / img.height
            new_width = int(img.width * ratio)
            img = img.resize((new_width, min_height), Image.Resampling.LANCZOS)
        resized_images.append(img)

    # 计算总宽度和最大高度
    total_width = sum(img.width for img in resized_images)
    max_height = min_height  # 因为我们已经调整了高度

    # 创建新图片
    new_image = Image.new('RGB', (total_width, max_height))

    # 拼接图片
    x_offset = 0
    for img in resized_images:
        new_image.paste(img, (x_offset, 0))
        x_offset += img.width

    return new_image


def generate_non_overlapping_rectangles(mask, num_rectangles, min_w=30, max_w=100, min_h=10, max_h=100):
    """
    在二值 mask 的背景区域生成不重叠的矩形框。

    Args:
        mask (PIL.Image): 二值 mask（前景=255/1，背景=0）
        num_rectangles (int): 要生成的矩形数量
        min_w (int): 矩形最小宽度
        max_w (int): 矩形最大宽度
        min_h (int): 矩形最小高度
        max_h (int): 矩形最大高度

    Returns:
        list: 生成的矩形框列表 [(x1, y1, x2, y2), ...]
    """
    mask_np = np.array(mask)
    height, width = mask_np.shape
    rectangles = []

    for _ in range(num_rectangles):
        attempts = 0
        max_attempts = 1000  # 避免无限循环
        success = False

        while attempts < max_attempts and not success:
            # 随机生成矩形宽高
            w = random.randint(min_w, max_w)
            h = random.randint(min_h, max_h)

            # 随机生成左上角坐标 (x1, y1)
            x1 = random.randint(0, width - w)
            y1 = random.randint(0, height - h)
            x2 = x1 + w
            y2 = y1 + h

            # 检查矩形是否在背景区域（mask=0）
            patch = mask_np[y1:y2, x1:x2]
            if np.all(patch == 0):
                # 检查是否与已有矩形重叠
                overlap = False
                for (rx1, ry1, rx2, ry2) in rectangles:
                    if not (x2 <= rx1 or x1 >= rx2 or y2 <= ry1 or y1 >= ry2):
                        overlap = True
                        break

                if not overlap:
                    rectangles.append((x1, y1, x2, y2))
                    success = True

            attempts += 1

        if not success:
            print(f"无法生成第 {len(rectangles) + 1} 个矩形，可能空间不足")
            break

    return rectangles
def equal_img(im_image,gt_image,mask_image,print_str=''):
    print(f'线程:{os.getpid()}')
    # 加载筛选逻辑，删除image和gt 不一致的jsonl地址，然后重新训练模型
    im_np = np.array(im_image)
    gt_np = np.array(gt_image)
    mask_np = np.array(mask_image)
    mask_np = (mask_np > 0).astype(np.uint8) * 255  # 转为 0 或 255
    kernel_size = 5  # 膨胀程度，值越大膨胀越宽
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
    # 执行膨胀
    mask_np = cv2.dilate(mask_np, kernel, iterations=10)
    im_np_mask = np.where(mask_np[..., np.newaxis] == 255, 0, im_np)
    gt_np_mask = np.where(mask_np[..., np.newaxis] == 255, 0, gt_np)
    equal=np.array_equal(im_np_mask,gt_np_mask)
    # 要是不一致直接保存下来
    if equal==False:
        print(f'{print_str}:俩张图数据不一样..........')
        return False
    else:
        return True
if __name__ == '__main__':
    print('sa')
    # from torch.utils.data import DataLoader
    #
    # # 动态分辨率函数
    # # def dynamic_resolution(resol):
    # #     return random.choice([256, 512, 1024])
    #
    # def dynamic_resolution():
    #     return random.choice([256, 512, 1024])
    #
    # # 初始化数据集
    # dataset = MyCustomDataset(data_root=r"/aicamera-mlp/hz_datasets/xiaochu_dataset/", jsonal_dataset_dir=r"/aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_clear_json",mode='train', debug=False)
#     # pudb.set_trace()
#     # 初始化数据加载器，指定自定义 collate_fn
#     loader1 = DataLoader(
#         dataset,
#         batch_size=1,
#         shuffle=True,
#         # collate_fn=lambda batch: dynamic_collate_fn(batch, dynamic_resolution)  # 动态分辨率
#         collate_fn=lambda batch: dynamic_collate_fn(batch, lambda: 512)  # 动态分辨率
#     )
#     dataset2 = MyCustomDataset(data_root=r"/aicamera-mlp/hz_datasets/xiaochu_dataset/", jsonal_dataset_dir=r"/aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_clear_json", mode='val', debug=False)
#     loader2 = DataLoader(
#         dataset2,
#         batch_size=1,
#         shuffle=True,
#         collate_fn=lambda batch: dynamic_collate_fn(batch, lambda: 512)  # 固定分辨率
#     )
#     # dataset3 = MyCustomDataset(data_root=r"/aicamera-mlp/hz_datasets/xiaochu_dataset/", jsonal_dataset_dir=r"/aicamera-mlp/hz_datasets/xiaochu_dataset/pdf_color_alll_json_test/", mode='test',
#     #                           debug=False)
#     # loader3 = DataLoader(
#     #     dataset2,
#     #     batch_size=1,
#     #     shuffle=True,
#     #     collate_fn=lambda batch: dynamic_collate_fn(batch, lambda: 512)  # 固定分辨率
#     # )
#     visual_dataset_path=r'/aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_json/visualization'
#     if os.path.exists(visual_dataset_path):
#         shutil.rmtree(visual_dataset_path)
#     os.makedirs(visual_dataset_path,exist_ok=True)
#     im_folder_path=os.path.join(visual_dataset_path,'im')
#     mask_folder_path=os.path.join(visual_dataset_path,'mask')
#     gt_folder_path=os.path.join(visual_dataset_path,'gt')
#     show_folder_path=os.path.join(visual_dataset_path,'show')
#     os.makedirs(im_folder_path,exist_ok=True)
#     os.makedirs(mask_folder_path,exist_ok=True)
#     os.makedirs(gt_folder_path,exist_ok=True)
#     os.makedirs(show_folder_path,exist_ok=True)
#     c=0
#     # 可视化数据集
#     # 遍历数据加载器
#     equal_list = []
#     problem_img_paths=[]
#     problem_show_img=[]
#     for flag, batch in walk_dataloaders([ ("loader1", loader1),("loader2", loader2)]): #
#         if c>300:
#             break
#         c+=1
#         bacth_im_image=batch['im']
#         bacth_mask_image=batch['mask']
#         bacth_gt_image=batch['gt']
#         im_path = batch['imagepath']
#         im_image=denormalize_tensor_image_rgb(bacth_im_image.squeeze(0))
#         mask_image=denormalize_mask(bacth_mask_image.squeeze(0))
#         gt_image=denormalize_tensor_image_rgb(bacth_gt_image.squeeze(0))
#         mask_image=mask_image.convert('L')
#         res_img = horizontal_concat_images([im_image, gt_image,mask_image])
#         res_equal=equal_img(im_image,gt_image,mask_image)
#
#         if res_equal==False:
#             problem_show_img.append(res_img)
#         # 将img和gt做diff判断图片是否有色差
#         print(f'可视化图片：{c} 完成...')
#         # print(f'可视化图片:{im_path[0]} 完成...')
#         # print(bacth_im_image.shape,bacth_mask_image.shape,bacth_gt_image.shape)
#         # print(f"{flag}: {len(batch['im'])} images at resolution {batch['resolution']}")
#     # 有问题的图片数量
#     print(f'有问题的图片数量:{len(problem_show_img)} 占比:{len(problem_show_img)}/{300}')
#     # print(problem_show_img)
#     import uuid
#     save_folder_path=r'/aicamera-mlp/hz_datasets/xiaochu_dataset/test_data/uvdoc_test/problem_datastes_img/'
#     if os.path.exists(save_folder_path):
#         shutil.rmtree(save_folder_path)
#     os.makedirs(save_folder_path,exist_ok=True)
#     for img in problem_show_img:
#         img_name=f'{str(uuid.uuid4())[:4]}.png'
#         img.save(os.path.join(save_folder_path,img_name))
