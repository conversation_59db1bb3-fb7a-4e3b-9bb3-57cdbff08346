#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自然场景+彩色块文字消除数据集生成脚本

技术要点和设计规格：

1. 彩色块生成方式：
   - 形状分布：60%圆角矩形，30%椭圆/圆形，10%随机多边形/斑块
   - 颜色：随机HSV，饱和度S[0.5,1.0]，明度V[0.4,0.9]，15%概率生成淡彩色块
   - 数量：每张图片生成1个彩色块
   - 大小：占图片总面积的5%-30%

2. 文字与彩色块关系：
   - 放置：文字包围盒在彩色块内随机放置，保持5%安全边距
   - 颜色：90%白色文字，10%彩色文字（与背景形成高对比度）
   - 彩色字颜色：根据背景亮度自动选择对比色

3. 彩色块与文字尺寸关系：
   - 40%紧凑型：色块1.2-1.5倍文字包围盒
   - 50%常规型：色块1.5-3倍文字包围盒  
   - 10%宽松型：色块3倍以上文字包围盒

4. Mask生成要求：
   - 覆盖范围：只覆盖文字区域
   - 形状：遵照nature_generate_script.py的不规则文字轮廓
   - 边界限制：严格保证不超出彩色块边界

5. 输出要求：
   - GT图像：被mask覆盖区域显示彩色块颜色和纹理
   - 其他区域与输入图像保持一致

6. (新增) 字体字号多样性：
   - 动态字号：基于图片短边的3%-15%
   - 自适应尺寸：确保在不同分辨率下视觉一致性
   - 避免极端情况：字号既不会太小也不会太大

7. (更新) Mask策略多样性：
   - 30%全部涂抹：整行文字全覆盖
   - 40%部分涂抹：随机选择30%-70%的字符
   - 30%单个涂抹：只涂抹1个字符（最难样本）
   - 所有mask严格限制在彩色块内部

基于nature_generate_script.py的结构，复用其字体、字号、mask生成方式
"""

import os
import random
import shutil
import math
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from render_font_proj import (
    load_font_from_tif, resize_image_with_aspect_ratio,
    create_blank_a4, generate_text_lines, render_text,
    create_combined_image
)
from multiprocessing import Manager, Pool
import colorsys

def init_shared_state(image_files):
    manager = Manager()
    shared = manager.Namespace()
    shared.image_index = 0
    shared.image_path_cache = []
    shared.image_files = image_files
    shared.total_images = len(image_files)
    shared.lock = manager.Lock()
    # 添加实际生成数量计数器
    shared.actual_generated = 0
    return shared

# 配置参数 - 根据数据集制作规范调整
# 获取脚本所在目录，然后构建相对于项目根目录的路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))  # 向上两级到项目根目录

FONT_PATH = os.path.join(project_root, "resource", "font")  # 字体文件夹路径
IMAGE_PATH = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/nature/"  # 背景图片文件夹路径
OUTPUT_PATH = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/nature_colorblock_full_inpainting_dataset"  # 输出文件夹路径

# 如果需要使用相对路径，请根据实际情况修改以下路径：
# IMAGE_PATH = os.path.join(project_root, "resource", "nature_images")
# OUTPUT_PATH = os.path.join(project_root, "output", "nature_colorblock_dataset")

# 字体类型及其抽取概率（复用原脚本配置）
# 暂时禁用繁体中文字体，因为字体文件不足
FONT_PROB = {
    "TraditionalChinese": 0,  # 暂时禁用，因为字体文件不足
    "SimplifiedChinese": 6,   # 增加简体中文的权重
    "Number": 3,
    "Letter": 2,
    "Punctuation": 2
}

# 样本生成参数
SAMPLES_PER_FOLDER = 1500  # 每个文件夹需要生成的样本数
# 字号现在基于图片短边的百分比，增加小字体覆盖
FONT_SIZE_RATIO_RANGE = (0.03, 0.18)  # 字号占图片短边的3%-18%，增加小字体覆盖
LINE_SPACING_RANGE = (10, 30)  # 行间距范围
CHAR_SPACING_RANGE = (3, 8)  # 字符间距范围

# 彩色块配置参数 - 增加小色块覆盖，让尺寸分布更广
COLORBLOCK_AREA_RATIO_RANGE = (0.008, 0.08)  # 彩色块面积占图片面积比例，增加小色块覆盖：0.8%-8%
COLORBLOCK_SHAPE_PROB = {
    'rounded_rect': 0.7,  # 圆角矩形（增加权重）
    'ellipse': 0.3        # 椭圆/圆形
    # 已移除不规则色块 'polygon'
}

# 文字颜色配置
WHITE_TEXT_PROB = 0.9  # 白色文字概率
COLORED_TEXT_PROB = 0.1  # 彩色文字概率

# 彩色块与文字尺寸关系配置 - 增加小色块覆盖，让尺寸分布更广
SIZE_RELATION_PROB = {
    'compact': (0.7, 1.05, 1.25),   # 70%概率，1.05-1.25倍，更紧凑，增加小色块
    'normal': (0.25, 1.25, 1.6),    # 25%概率，1.25-1.6倍，减小倍数
    'loose': (0.05, 1.6, 2.2)       # 5%概率，1.6-2.2倍，减少大色块概率
}

# Mask策略多样性配置
MASK_STRATEGY_PROB = {
    'whole_line': 0.3,      # 30%全部涂抹
    'partial_chars': 0.4,   # 40%部分涂抹
    'single_char': 0.3      # 30%单个涂抹
}

# 部分涂抹时的字符选择比例
PARTIAL_CHARS_RATIO_RANGE = (0.3, 0.7)  # 30%-70%的字符

def collect_font_paths():
    """收集所有字体文件路径，复用原脚本逻辑"""
    font_paths = {
        "TraditionalChinese": [],
        "SimplifiedChinese": [],
        "Number": [],
        "Letter": [],
        "Punctuation": []
    }

    # 收集 TraditionalChinese 和 SimplifiedChinese 的字体
    for font_type in ["TraditionalChinese", "SimplifiedChinese"]:
        font_dir = os.path.join(FONT_PATH, font_type)
        if not os.path.exists(font_dir):
            continue

        for root, _, files in os.walk(font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.otf')):
                    font_paths[font_type].append(os.path.join(root, file))

    # 收集 Other 文件夹下的字体，供 Number 和 Letter 使用
    other_font_dir = os.path.join(FONT_PATH, "Other")
    if os.path.exists(other_font_dir):
        for root, _, files in os.walk(other_font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.otf')):
                    font_file = os.path.join(root, file)
                    font_paths["Number"].append(font_file)
                    font_paths["Letter"].append(font_file)
                    font_paths["Punctuation"].append(font_file)
    return font_paths

def sample_fonts(font_paths, num_samples):
    """根据概率抽样字体，复用原脚本逻辑，智能处理字体不足的情况"""
    # 只考虑有可用字体的类型
    available_font_types = {font_type: prob for font_type, prob in FONT_PROB.items()
                           if font_paths.get(font_type, [])}

    if not available_font_types:
        print("错误: 没有任何可用的字体类型")
        return []

    total_prob = sum(available_font_types.values())
    type_samples = {}

    # 计算每种字体类型的样本数（只考虑可用的字体类型）
    for font_type, prob in available_font_types.items():
        count = int(num_samples * (prob / total_prob))
        type_samples[font_type] = count

    # 补足因浮点数计算导致的样本数差额
    remaining = num_samples - sum(type_samples.values())
    if remaining > 0:
        # 将剩余的样本分配给第一个可用的字体类型
        first_available_type = next(iter(available_font_types))
        type_samples[first_available_type] += remaining

    # 抽样并保存为元组 (font_type, font_path)
    sampled_font_pairs = []
    for font_type, count in type_samples.items():
        available_fonts = font_paths.get(font_type, [])
        if not available_fonts:
            print(f"警告: 字体类型 {font_type} 没有可用字体")
            continue

        if count <= 0:
            continue

        # 如果字体数量不足，允许重复抽样
        try:
            if count > len(available_fonts):
                sampled = random.choices(available_fonts, k=count)
            else:
                sampled = random.sample(available_fonts, k=count)
        except Exception as e:
            print(f"警告: 字体抽样失败，类型 {font_type}, 需要 {count} 个，可用 {len(available_fonts)} 个: {e}")
            continue

        # 处理字体路径并配对
        for path in sampled:
            if not path or not isinstance(path, str):
                print(f"警告: 无效的字体路径: {path}")
                continue
            # 如果是TTC字体文件，保持原路径不变（TTC文件可以直接使用）
            # 原来的 path[0] 逻辑是错误的，会导致路径变成单个字符
            processed_path = path
            sampled_font_pairs.append((font_type, processed_path))

    if len(sampled_font_pairs) < num_samples:
        print(f"警告: 字体抽样不足，需要 {num_samples} 个，只抽到 {len(sampled_font_pairs)} 个")

    return sampled_font_pairs

def collect_image_files(image_dir):
    """收集图片文件，复用原脚本逻辑"""
    print(f"正在收集图片文件，目录: {image_dir}")

    if not os.path.exists(image_dir):
        print(f"错误: 目录不存在: {image_dir}")
        return []

    if not os.path.isdir(image_dir):
        print(f"错误: 路径不是目录: {image_dir}")
        return []

    image_files = []
    for root, _, files in os.walk(image_dir):
        print(f"正在扫描目录: {root}, 文件数: {len(files)}")
        for file in files:
            if file.lower().endswith(('.jpg', '.png', '.jpeg')):
                full_path = os.path.join(root, file)
                image_files.append(full_path)
                if len(image_files) <= 3:  # 只显示前3个文件用于调试
                    print(f"  找到图片: {full_path}")

    print(f"总共收集到 {len(image_files)} 个图片文件")
    return image_files

def generate_colorblock_color():
    """
    生成彩色块颜色
    85%概率：饱和度S[0.5,1.0]，明度V[0.4,0.9]
    15%概率：淡彩色块S[0.2,0.5]，V[0.8,1.0]
    """
    if random.random() < 0.15:  # 15%概率生成淡彩色块
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.2, 0.5)
        v = random.uniform(0.8, 1.0)
    else:  # 85%概率生成鲜艳色块
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.5, 1.0)
        v = random.uniform(0.4, 0.9)
    
    rgb = colorsys.hsv_to_rgb(h, s, v)
    return tuple(int(c * 255) for c in rgb)

def calculate_brightness(color):
    """计算颜色亮度"""
    r, g, b = color
    return (0.299 * r + 0.587 * g + 0.114 * b) / 255.0

def generate_contrast_text_color(bg_color):
    """根据背景色生成高对比度的文字颜色"""
    bg_brightness = calculate_brightness(bg_color)

    if bg_brightness > 0.5:  # 背景亮色，生成暗色文字
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.3, 1.0)
        v = random.uniform(0.1, 0.4)  # 暗色
    else:  # 背景暗色，生成亮色文字
        h = random.randint(0, 360) / 360.0
        s = random.uniform(0.3, 1.0)
        v = random.uniform(0.6, 1.0)  # 亮色

    rgb = colorsys.hsv_to_rgb(h, s, v)
    return tuple(int(c * 255) for c in rgb)

def calculate_dynamic_font_sizes(image_size, num_lines, colorblock_size=None, shape_type='rounded_rect', chars_per_line=4, use_tight_fit=False):
    """
    基于彩色块大小计算动态字号，根据边界策略调整字体大小
    40%贴边模式（更贴合），60%安全边距模式
    """
    if colorblock_size:
        # 基于彩色块大小计算字号，确保文字能放入色块
        cb_width, cb_height = colorblock_size

        # 根据传入的边界策略调整安全边距
        if use_tight_fit:
            # 完全填满模式：文字尽可能填满整个色块
            if shape_type == 'polygon':
                margin_ratio = 0.05  # 不规则图形稍微保守，5%边距
            else:
                margin_ratio = 0.02   # 规则图形基本填满，2%边距
        else:
            # 安全边距模式：保持适当距离，确保文字完全在色块内
            if shape_type == 'polygon':
                margin_ratio = 0.20  # 不规则图形使用较大安全边距
            else:
                margin_ratio = 0.12  # 规则图形使用适中安全边距

        available_width = cb_width * (1 - 2 * margin_ratio)
        available_height = cb_height * (1 - 2 * margin_ratio)

        # 基于可用宽度计算字号（考虑字符间距）
        char_spacing = 5  # 字符间距
        char_spacing_total = (chars_per_line - 1) * char_spacing
        available_width_for_chars = available_width - char_spacing_total
        font_size_by_width = int(available_width_for_chars / (chars_per_line * 0.8))  # 0.8是字符宽度系数

        # 基于可用高度计算字号（考虑行间距）
        line_spacing = 15  # 行间距
        line_spacing_total = (num_lines - 1) * line_spacing
        available_height_for_chars = available_height - line_spacing_total
        font_size_by_height = int(available_height_for_chars / (num_lines * 1.2))  # 1.2是字符高度系数

        if use_tight_fit:
            # 贴边模式：优先让文字填满色块，取较大值但不超过限制
            base_font_size = max(font_size_by_width, font_size_by_height)
            # 但仍要确保不会导致任何字符超出50%
            max_safe_size = min(font_size_by_width * 1.5, font_size_by_height * 1.5)  # 允许一定超出
            base_font_size = min(base_font_size, max_safe_size)
        else:
            # 安全边距模式：取较小值确保文字完全放入
            base_font_size = min(font_size_by_width, font_size_by_height)

        # 确保字号在合理范围内
        base_font_size = max(20, min(base_font_size, 350))  # 最小20px，最大350px

        print(f"色块尺寸: {cb_width}x{cb_height}, 形状: {shape_type}, 安全边距: {margin_ratio*100:.0f}%")
        print(f"可用区域: {available_width:.1f}x{available_height:.1f}")
        print(f"基于宽度计算字号: {font_size_by_width}, 基于高度计算字号: {font_size_by_height}")
        print(f"最终基础字号: {base_font_size}")

    else:
        # 基于图片短边计算字号（备用方案）
        base_size = min(image_size)
        base_font_size = int(base_size * random.uniform(*FONT_SIZE_RATIO_RANGE))
        base_font_size = max(24, min(base_font_size, 400))

    font_sizes = []

    for _ in range(num_lines):
        # 在基础字号基础上添加较小的随机变化（±10%），让文字更一致更大
        variation = random.uniform(0.9, 1.1)
        font_size = int(base_font_size * variation)
        # 确保字号在合理范围内
        font_size = max(24, min(font_size, 350))
        font_sizes.append(font_size)

    return font_sizes

def choose_mask_strategy():
    """选择Mask策略"""
    rand = random.random()
    cumulative = 0

    for strategy, prob in MASK_STRATEGY_PROB.items():
        cumulative += prob
        if rand <= cumulative:
            return strategy

    # 默认返回部分涂抹
    return 'partial_chars'

def choose_size_relation():
    """选择彩色块与文字的尺寸关系"""
    rand = random.random()
    cumulative = 0

    for relation_type, (prob, min_ratio, max_ratio) in SIZE_RELATION_PROB.items():
        cumulative += prob
        if rand <= cumulative:
            ratio = random.uniform(min_ratio, max_ratio)
            return relation_type, ratio

    # 默认返回常规型
    return 'normal', random.uniform(1.5, 3.0)

def calculate_text_actual_bbox(lines, line_spacing=15, char_spacing=5):
    """
    计算文字的实际渲染边界框

    参数:
        lines: 文本行列表
        line_spacing: 行间距
        char_spacing: 字符间距

    返回:
        dict: {'width': 宽度, 'height': 高度, 'min_x': 最小x, 'max_x': 最大x, 'min_y': 最小y, 'max_y': 最大y}
    """
    if not lines:
        return None

    min_x = float('inf')
    max_x = float('-inf')
    min_y = float('inf')
    max_y = float('-inf')

    current_y = 0

    for line_idx, line in enumerate(lines):
        if not line['chars']:
            continue

        # 计算行高度
        line_height = max(char['height'] for char in line['chars'])

        # 计算行宽度
        line_width = 0
        current_x = 0

        for char_idx, char in enumerate(line['chars']):
            # 获取字体对象
            line_font = line['font']

            # 使用字体的getbbox方法获取字符的实际渲染边界
            char_bbox = line_font.getbbox(char['char'])
            bbox_left, bbox_top, bbox_right, bbox_bottom = char_bbox

            # 计算字符的实际渲染边界
            char_left = current_x + bbox_left
            char_right = current_x + bbox_right
            char_top = current_y + line_height + bbox_top  # 基线位置 + bbox偏移
            char_bottom = current_y + line_height + bbox_bottom

            # 更新总边界
            min_x = min(min_x, char_left)
            max_x = max(max_x, char_right)
            min_y = min(min_y, char_top)
            max_y = max(max_y, char_bottom)

            # 移动到下一个字符位置
            current_x += char['width'] + char_spacing

        # 移动到下一行
        current_y += line_height + line_spacing

    if min_x == float('inf'):
        return None

    return {
        'width': max_x - min_x,
        'height': max_y - min_y,
        'min_x': min_x,
        'max_x': max_x,
        'min_y': min_y,
        'max_y': max_y
    }

def generate_colorblock_for_text_bbox(text_bbox, shape_type, use_tight_fit, image_size):
    """
    根据文字的实际渲染边界框生成完全包围的彩色块
    确保文字严格在色块内部，绝不超出

    参数:
        text_bbox: 文字的实际渲染边界框 {'min_x', 'min_y', 'max_x', 'max_y', 'width', 'height'}
        shape_type: 色块形状类型
        use_tight_fit: 是否使用紧贴模式
        image_size: 图像尺寸

    返回:
        (cb_width, cb_height, cb_x, cb_y, shape_mask, text_offset_x, text_offset_y)
    """
    img_width, img_height = image_size

    # 获取文字的实际边界
    text_min_x = text_bbox['min_x']
    text_min_y = text_bbox['min_y']
    text_max_x = text_bbox['max_x']
    text_max_y = text_bbox['max_y']
    text_width = text_bbox['width']
    text_height = text_bbox['height']

    # 根据紧贴模式决定边距，确保足够的安全边距
    if use_tight_fit:  # 80%概率：紧贴模式
        margin = 15  # 增加边距，确保文字不会超出
        fit_type = "紧贴模式"
    else:  # 20%概率：宽松模式
        margin = 30  # 增加边距，确保文字不会超出
        fit_type = "宽松模式"

    # 计算理想的色块尺寸（完全基于文字边界）
    ideal_cb_width = int(text_width + 2 * margin)
    ideal_cb_height = int(text_height + 2 * margin)

    # 计算理想的色块位置（让文字在色块中心）
    text_center_x = (text_min_x + text_max_x) / 2
    text_center_y = (text_min_y + text_max_y) / 2

    ideal_cb_x = int(text_center_x - ideal_cb_width / 2)
    ideal_cb_y = int(text_center_y - ideal_cb_height / 2)

    # 检查是否需要调整以适应图像边界
    # 计算图像边界约束
    max_width = img_width - 20  # 留10像素边距
    max_height = img_height - 20

    # 如果理想尺寸超出图像，拒绝生成此样本
    if ideal_cb_width > max_width or ideal_cb_height > max_height:
        print(f"❌ 色块尺寸超出图像边界，拒绝生成")
        print(f"   理想色块尺寸: {ideal_cb_width}x{ideal_cb_height}")
        print(f"   图像可用空间: {max_width}x{max_height}")
        print(f"   文字尺寸: {text_width:.1f}x{text_height:.1f}")
        return None  # 返回None表示失败

    # 使用理想尺寸（不缩放）
    cb_width = ideal_cb_width
    cb_height = ideal_cb_height

    # 重新计算位置，确保色块在图像内
    cb_x = int(text_center_x - cb_width / 2)
    cb_y = int(text_center_y - cb_height / 2)

    # 边界约束
    cb_x = max(10, min(cb_x, img_width - cb_width - 10))
    cb_y = max(10, min(cb_y, img_height - cb_height - 10))

    # 重新计算文字相对于最终色块的偏移量
    final_cb_center_x = cb_x + cb_width / 2
    final_cb_center_y = cb_y + cb_height / 2
    text_offset_x = text_center_x - final_cb_center_x
    text_offset_y = text_center_y - final_cb_center_y

    # 验证文字是否完全在色块内（关键验证步骤）
    final_text_min_x = text_min_x + text_offset_x
    final_text_max_x = text_max_x + text_offset_x
    final_text_min_y = text_min_y + text_offset_y
    final_text_max_y = text_max_y + text_offset_y

    # 色块的内部边界（相对于色块左上角）
    cb_inner_margin = 5  # 内部安全边距
    cb_inner_left = cb_inner_margin
    cb_inner_right = cb_width - cb_inner_margin
    cb_inner_top = cb_inner_margin
    cb_inner_bottom = cb_height - cb_inner_margin

    # 检查文字是否超出色块内部边界
    text_exceeds = False
    if (final_text_min_x - cb_x < cb_inner_left or
        final_text_max_x - cb_x > cb_inner_right or
        final_text_min_y - cb_y < cb_inner_top or
        final_text_max_y - cb_y > cb_inner_bottom):
        text_exceeds = True
        print(f"❌ 警告：文字可能超出色块边界！")
        print(f"   文字在色块中的位置: ({final_text_min_x - cb_x:.1f}, {final_text_min_y - cb_y:.1f}) -> ({final_text_max_x - cb_x:.1f}, {final_text_max_y - cb_y:.1f})")
        print(f"   色块内部安全区域: ({cb_inner_left}, {cb_inner_top}) -> ({cb_inner_right}, {cb_inner_bottom})")

        # 强制调整偏移量，确保文字在色块内
        if final_text_min_x - cb_x < cb_inner_left:
            text_offset_x += (cb_inner_left - (final_text_min_x - cb_x))
        if final_text_max_x - cb_x > cb_inner_right:
            text_offset_x -= ((final_text_max_x - cb_x) - cb_inner_right)
        if final_text_min_y - cb_y < cb_inner_top:
            text_offset_y += (cb_inner_top - (final_text_min_y - cb_y))
        if final_text_max_y - cb_y > cb_inner_bottom:
            text_offset_y -= ((final_text_max_y - cb_y) - cb_inner_bottom)

        print(f"   调整后偏移: ({text_offset_x:.1f}, {text_offset_y:.1f})")

    # 创建形状mask
    from PIL import Image, ImageDraw
    shape_mask = Image.new('L', (cb_width, cb_height), 0)
    draw = ImageDraw.Draw(shape_mask)

    if shape_type == 'rounded_rect':
        # 圆角矩形
        corner_radius = min(cb_width, cb_height) // 8
        draw.rounded_rectangle([0, 0, cb_width-1, cb_height-1], radius=corner_radius, fill=255)
    elif shape_type == 'ellipse':
        # 椭圆
        draw.ellipse([0, 0, cb_width-1, cb_height-1], fill=255)

    print(f"色块生成策略: {fit_type}, 边距: {margin}px")
    print(f"文字边界: ({text_min_x:.1f},{text_min_y:.1f})-({text_max_x:.1f},{text_max_y:.1f})")
    print(f"生成色块: ({cb_x},{cb_y}) 尺寸: {cb_width}x{cb_height}")
    print(f"文字偏移: ({text_offset_x:.1f},{text_offset_y:.1f})")
    if text_exceeds:
        print(f"✅ 已强制调整文字位置，确保在色块内")

    return cb_width, cb_height, cb_x, cb_y, shape_mask, text_offset_x, text_offset_y

def generate_colorblock_shape(image_size, area_ratio, shape_type):
    """
    生成彩色块的形状和位置
    返回：(x, y, width, height, shape_mask)
    """
    img_width, img_height = image_size
    target_area = img_width * img_height * area_ratio

    # 计算彩色块的宽高，保持合理的宽高比
    aspect_ratio = random.uniform(0.5, 2.0)  # 宽高比范围
    width = int(math.sqrt(target_area * aspect_ratio))
    height = int(target_area / width)

    # 确保不超出图像边界
    width = min(width, img_width - 20)
    height = min(height, img_height - 20)

    # 随机选择位置
    x = random.randint(10, img_width - width - 10)
    y = random.randint(10, img_height - height - 10)

    # 创建形状mask
    shape_mask = Image.new('L', (width, height), 0)
    draw = ImageDraw.Draw(shape_mask)

    if shape_type == 'rounded_rect':
        # 圆角矩形
        corner_radius = min(width, height) // 8
        draw.rounded_rectangle([0, 0, width-1, height-1], radius=corner_radius, fill=255)

    elif shape_type == 'ellipse':
        # 椭圆
        draw.ellipse([0, 0, width-1, height-1], fill=255)

    elif shape_type == 'polygon':
        # 随机多边形
        center_x, center_y = width // 2, height // 2
        radius_x, radius_y = width // 2 - 5, height // 2 - 5
        num_points = random.randint(5, 8)

        points = []
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            # 添加随机扰动
            r_x = radius_x * random.uniform(0.7, 1.0)
            r_y = radius_y * random.uniform(0.7, 1.0)
            px = center_x + r_x * math.cos(angle)
            py = center_y + r_y * math.sin(angle)
            points.append((px, py))

        draw.polygon(points, fill=255)

    return x, y, width, height, shape_mask

def estimate_text_size(font_paths, font_sizes, num_lines, chars_per_line):
    """估算文字的大小，用于确定彩色块尺寸"""
    # 使用平均字体大小和字符数来估算
    avg_font_size = sum(font_sizes) / len(font_sizes)
    avg_chars_per_line = chars_per_line if isinstance(chars_per_line, int) else sum(chars_per_line) / len(chars_per_line)

    # 估算文字区域大小（粗略估算）
    char_width = avg_font_size * 0.8  # 平均字符宽度
    char_height = avg_font_size * 1.2  # 字符高度

    text_width = int(avg_chars_per_line * char_width)
    text_height = int(num_lines * char_height)

    return text_width, text_height

def generate_text_colors(num_lines, colorblock_color):
    """生成文字颜色列表"""
    text_colors = []

    for _ in range(num_lines):
        if random.random() < WHITE_TEXT_PROB:
            # 90%概率生成白色文字
            text_colors.append((255, 255, 255))
        else:
            # 10%概率生成与背景对比的彩色文字
            text_colors.append(generate_contrast_text_color(colorblock_color))

    return text_colors

def generate_samples_task(args):
    """生成样本的任务函数，基于原脚本结构修改"""
    output_folder, shared, samples_to_generate, process_id = args
    if not shared:
        raise ValueError("Shared list is empty in process %d" % process_id)

    # 检查共享状态
    if not hasattr(shared, 'image_files') or not shared.image_files:
        raise ValueError(f"[进程 {process_id}] 共享图片列表为空")

    if not hasattr(shared, 'total_images') or shared.total_images <= 0:
        raise ValueError(f"[进程 {process_id}] 图片总数无效: {getattr(shared, 'total_images', 'None')}")

    print(f"[进程 {process_id}] 开始生成，图片总数: {shared.total_images}")

    # 收集字体文件
    font_paths = collect_font_paths()

    # 检查字体可用性
    total_fonts = sum(len(fonts) for fonts in font_paths.values())
    if total_fonts == 0:
        print(f"[进程 {process_id}] 错误: 没有找到任何可用字体")
        return

    print(f"[进程 {process_id}] 可用字体统计:")
    for font_type, fonts in font_paths.items():
        print(f"  {font_type}: {len(fonts)} 个字体")

    # 如果字体总数太少，调整生成策略
    if total_fonts < 10:
        print(f"[进程 {process_id}] 警告: 字体数量较少({total_fonts}个)，可能影响生成质量")

    generated = 0
    failed_attempts = 0
    max_failed_attempts = samples_to_generate * 2  # 允许失败次数为目标样本数的2倍

    while generated < samples_to_generate and failed_attempts < max_failed_attempts:
        try:
            # 选择图片
            with shared.lock:
                # 再次检查以防竞态条件
                if not shared.image_files or shared.total_images <= 0:
                    raise ValueError(f"[进程 {process_id}] 共享状态异常: 图片列表长度={len(shared.image_files) if shared.image_files else 0}, 总数={shared.total_images}")

                current_index = shared.image_index
                shared.image_index += 1
                image_path = shared.image_files[current_index % shared.total_images]

            # 检查图片文件是否存在和有效
            if not os.path.exists(image_path):
                print(f"[进程 {process_id}] 图片文件不存在: {image_path}")
                failed_attempts += 1
                continue

            try:
                # 加载背景图片获取尺寸
                bg_image = Image.open(image_path)
                image_size = bg_image.size
                bg_image.close()  # 及时关闭图片
            except Exception as e:
                print(f"[进程 {process_id}] 无法加载图片 {image_path}: {e}")
                failed_attempts += 1
                continue

            # 生成字体参数
            num_lines = random.randint(2, 4)
            chars_per_line = random.randint(3, 6)

            # 随机抽样字体
            fonts_type = sample_fonts(font_paths, num_lines)
            if not fonts_type:
                print(f"[进程 {process_id}] 警告: 字体抽样失败，跳过该样本")
                failed_attempts += 1
                continue

            # 生成字体大小
            base_size = min(image_size)
            font_sizes = []
            for _ in range(num_lines):
                font_size = int(base_size * random.uniform(*FONT_SIZE_RATIO_RANGE))
                font_size = max(24, min(font_size, 350))
                font_sizes.append(font_size)

            print(f"[进程 {process_id}] 生成参数: {num_lines}行x{chars_per_line}字符, 字体大小: {font_sizes}")

            # 调用新的生成函数
            success = generate_colorblock_erasure_dataset(
                output_folder,
                1,
                current_index,
                fonts_type,  # 传递有效的字体路径
                font_sizes,  # 传递有效的字体大小
                None,
                dpi=300,
                erasure_mode='random',
                expand_boundary=True,
                multi_line_erasure=True,
                left_right_expand_boundary_ratio=0.3,
                line_spacing=15,
                char_spacing=5,
                num_lines=num_lines,
                chars_per_line=chars_per_line,
                use_image_background=True,
                image_paths=image_path,
                text_colors=None,
                img_name=os.path.basename(image_path),
                target_size=image_size
            )

            if success:
                generated += 1
                # 更新共享计数器
                with shared.lock:
                    shared.actual_generated += 1
                # 进度显示
                if generated % 10 == 0:
                    print(f"[进程 {process_id}] 已生成 {generated}/{samples_to_generate} 个样本")
            else:
                failed_attempts += 1

        except Exception as e:
            print(f"[进程 {process_id}] 生成样本时出错: {e}")
            failed_attempts += 1
            continue

    if failed_attempts >= max_failed_attempts:
        print(f"[进程 {process_id}] 警告：失败次数过多，提前结束。已生成 {generated}/{samples_to_generate} 个样本")

def check_char_overlap(new_char_bounds, existing_chars, min_spacing=2):
    """
    检查新字符是否与已存在的字符重叠

    参数:
        new_char_bounds: 新字符的边界 (left, top, right, bottom)
        existing_chars: 已存在的字符列表
        min_spacing: 最小间距

    返回:
        bool: True表示重叠，False表示不重叠
    """
    new_left, new_top, new_right, new_bottom = new_char_bounds

    for char in existing_chars:
        if 'actual_left' in char:
            char_left = char['actual_left']
            char_right = char['actual_right']
            char_top = char['actual_top']
            char_bottom = char['actual_bottom']
        else:
            char_left = char['x']
            char_right = char['x'] + char['width']
            char_top = char['y'] - char['height']
            char_bottom = char['y']

        # 检查水平重叠
        horizontal_overlap = not (new_right + min_spacing <= char_left or new_left >= char_right + min_spacing)
        # 检查垂直重叠
        vertical_overlap = not (new_bottom + min_spacing <= char_top or new_top >= char_bottom + min_spacing)

        if horizontal_overlap and vertical_overlap:
            return True

    return False

def position_text_in_colorblock_center(lines, cb_x, cb_y, cb_width, cb_height, text_offset_x=0, text_offset_y=0,
                                      line_spacing=10, char_spacing=5):
    """
    将文字定位在色块中心，确保文字完全在色块内部

    参数:
        lines: 文本行列表
        cb_x, cb_y, cb_width, cb_height: 彩色块的位置和尺寸
        text_offset_x, text_offset_y: 文字相对于色块中心的偏移量
        line_spacing: 行间距
        char_spacing: 字符间距

    返回:
        positioned_lines: 带位置信息的文本行列表
    """
    positioned_lines = []
    all_positioned_chars = []  # 跟踪所有已定位的字符，用于重叠检测

    # 计算色块中心点
    cb_center_x = cb_x + cb_width / 2
    cb_center_y = cb_y + cb_height / 2

    # 应用文字偏移量
    text_center_x = cb_center_x + text_offset_x
    text_center_y = cb_center_y + text_offset_y

    print(f"色块中心: ({cb_center_x:.1f}, {cb_center_y:.1f})")
    print(f"文字中心: ({text_center_x:.1f}, {text_center_y:.1f}), 偏移: ({text_offset_x:.1f}, {text_offset_y:.1f})")

    # 计算总文本尺寸
    total_text_width = 0
    total_text_height = 0

    for line in lines:
        if line['chars']:
            line_width = sum(char['width'] for char in line['chars']) + (len(line['chars']) - 1) * char_spacing
            line_height = max(char['height'] for char in line['chars'])
            total_text_width = max(total_text_width, line_width)
            total_text_height += line_height

    # 添加行间距
    if len(lines) > 1:
        total_text_height += (len(lines) - 1) * line_spacing

    # 计算文字块的起始位置（基于中心点）
    text_start_x = text_center_x - total_text_width / 2
    text_start_y = text_center_y - total_text_height / 2

    # 确保文字块在色块内部（添加安全边距）
    safety_margin = 10
    min_text_x = cb_x + safety_margin
    max_text_x = cb_x + cb_width - safety_margin - total_text_width
    min_text_y = cb_y + safety_margin
    max_text_y = cb_y + cb_height - safety_margin - total_text_height

    # 检查文字是否能放入色块
    print(f"🔍 约束检查: max_text_x={max_text_x:.1f}, min_text_x={min_text_x:.1f}")
    print(f"🔍 约束检查: max_text_y={max_text_y:.1f}, min_text_y={min_text_y:.1f}")

    if max_text_x < min_text_x or max_text_y < min_text_y:
        print(f"❌ 错误: 文字太大，无法放入色块")
        print(f"   文字尺寸: {total_text_width:.1f}x{total_text_height:.1f}")
        print(f"   色块尺寸: {cb_width:.1f}x{cb_height:.1f}")
        print(f"   色块可用空间: {cb_width - 2*safety_margin:.1f}x{cb_height - 2*safety_margin:.1f}")
        print(f"   需要的空间: {total_text_width:.1f}x{total_text_height:.1f}")
        return []  # 返回空列表表示失败

    # 约束文字块位置
    text_start_x = max(min_text_x, min(text_start_x, max_text_x))
    text_start_y = max(min_text_y, min(text_start_y, max_text_y))

    print(f"文字块尺寸: {total_text_width:.1f}x{total_text_height:.1f}")
    print(f"色块约束: X[{min_text_x:.1f}, {max_text_x:.1f}], Y[{min_text_y:.1f}, {max_text_y:.1f}]")
    print(f"文字块起始位置: ({text_start_x:.1f}, {text_start_y:.1f})")

    positioned_lines = []
    current_y = text_start_y

    for line_idx, line in enumerate(lines):
        if not line['chars']:
            continue

        # 计算行高度和宽度
        line_height = max(char['height'] for char in line['chars'])
        line_width = sum(char['width'] for char in line['chars']) + (len(line['chars']) - 1) * char_spacing

        # 计算行的起始X位置（水平居中，但确保在色块内）
        ideal_start_x = text_center_x - line_width / 2

        # 确保行在色块边界内
        min_line_x = cb_x + safety_margin
        max_line_x = cb_x + cb_width - safety_margin - line_width
        start_x = max(min_line_x, min(ideal_start_x, max_line_x))

        positioned_chars = []
        current_x = start_x

        print(f"处理第{line_idx}行: 宽度{line_width:.1f}, 高度{line_height}, 理想X: {ideal_start_x:.1f}, 实际X: {start_x:.1f}")

        for char_idx, char in enumerate(line['chars']):
            # 计算字符的渲染位置（左上角坐标）
            char_x = current_x
            char_y = current_y + line_height - char['height']  # 底部对齐

            # 存储字符位置信息
            positioned_char = {
                'char': char['char'],
                'x': char_x,
                'y': char_y + char['height'],  # 使用底部坐标作为基线
                'width': char['width'],
                'height': char['height'],
                'font': char['font'],
                'color': char.get('color', (0, 0, 0)),
                'index': char.get('index', char_idx)  # 添加缺失的index字段
            }

            positioned_chars.append(positioned_char)
            current_x += char['width'] + char_spacing

        # 存储行信息
        positioned_line = {
            'chars': positioned_chars,
            'height': line_height,
            'y': current_y,
            'index': line_idx,  # 添加缺失的index字段
            'font': line.get('font')  # 添加字体信息
        }
        positioned_lines.append(positioned_line)

        # 移动到下一行
        current_y += line_height + line_spacing

    return positioned_lines


def get_text_size(text, font):
    """
    获取文字的渲染尺寸

    参数:
        text: 文字内容
        font: 字体对象

    返回:
        (width, height): 文字的宽度和高度
    """
    bbox = font.getbbox(text)
    width = bbox[2] - bbox[0]
    height = bbox[3] - bbox[1]
    return width, height


def calculate_text_bbox(positioned_lines):
    """
    计算所有文字的实际渲染边界框

    参数:
        positioned_lines: 带位置信息的文本行列表

    返回:
        text_bbox: {'min_x', 'min_y', 'max_x', 'max_y', 'width', 'height'}
    """
    if not positioned_lines:
        return None

    min_x = float('inf')
    min_y = float('inf')
    max_x = float('-inf')
    max_y = float('-inf')

    for line in positioned_lines:
        for char in line['chars']:
            char_left = char['x']
            char_right = char['x'] + char['width']
            char_top = char['y'] - char['height']
            char_bottom = char['y']

            min_x = min(min_x, char_left)
            max_x = max(max_x, char_right)
            min_y = min(min_y, char_top)
            max_y = max(max_y, char_bottom)

    return {
        'min_x': min_x,
        'min_y': min_y,
        'max_x': max_x,
        'max_y': max_y,
        'width': max_x - min_x,
        'height': max_y - min_y
    }

def create_colorblock_mask_image(image_size, erasure_regions, original_image):
    """
    创建彩色块专用的掩码图像，覆盖整个色块区域，使用圆润的mask效果

    参数:
        image_size: 图像尺寸 (width, height)
        erasure_regions: 要抹除的区域列表（包含色块信息）
        original_image: 原始图像，用于生成可视化

    返回:
        mask_image: 掩码图像
        show_img: 可视化图像
    """
    import numpy as np
    import cv2
    from PIL import Image, ImageDraw, ImageFilter

    width, height = image_size

    # 创建NumPy数组用于更精细的mask操作
    mask_np = np.zeros((height, width), dtype=np.uint8)

    # 创建可视化图像
    show_img = original_image.copy() if original_image else Image.new('RGB', (width, height), (255, 255, 255))
    show_draw = ImageDraw.Draw(show_img)

    # 遍历所有抹除区域
    for region in erasure_regions:
        if region.get('is_full_colorblock', False):
            # 这是一个覆盖整个色块的mask
            cb_x = region['x']
            cb_y = region['y']
            cb_width = region['width']
            cb_height = region['height']

            # 获取色块的形状信息
            shape_type = region.get('shape_type', 'rounded_rect')

            # 根据色块形状生成对应的mask
            if shape_type == 'ellipse':
                # 椭圆形色块：生成椭圆形mask并向外扩展
                center_x = cb_x + cb_width // 2
                center_y = cb_y + cb_height // 2

                # 向外扩展的像素数
                expand_pixels = 8  # 向外扩展8像素

                # 扩展后的椭圆参数
                expanded_width = cb_width + 2 * expand_pixels
                expanded_height = cb_height + 2 * expand_pixels
                expanded_x = cb_x - expand_pixels
                expanded_y = cb_y - expand_pixels

                # 确保扩展后的椭圆不超出图像边界
                expanded_x = max(0, expanded_x)
                expanded_y = max(0, expanded_y)
                expanded_width = min(expanded_width, width - expanded_x)
                expanded_height = min(expanded_height, height - expanded_y)

                # 重新计算中心点
                center_x = expanded_x + expanded_width // 2
                center_y = expanded_y + expanded_height // 2

                # 绘制椭圆mask
                cv2.ellipse(mask_np,
                            center=(center_x, center_y),
                            axes=(expanded_width // 2, expanded_height // 2),
                            angle=0,
                            startAngle=0,
                            endAngle=360,
                            color=255,
                            thickness=-1)

                # 对椭圆区域应用高斯模糊，让边缘更自然
                # 创建椭圆ROI
                y1_safe = max(0, expanded_y)
                y2_safe = min(height, expanded_y + expanded_height)
                x1_safe = max(0, expanded_x)
                x2_safe = min(width, expanded_x + expanded_width)

                if y1_safe < y2_safe and x1_safe < x2_safe:
                    roi = mask_np[y1_safe:y2_safe, x1_safe:x2_safe]
                    if roi.size > 0:
                        blurred_roi = cv2.GaussianBlur(roi, (7, 7), 3)
                        mask_np[y1_safe:y2_safe, x1_safe:x2_safe] = blurred_roi

                # 在可视化图像上绘制椭圆边框
                show_draw.ellipse([expanded_x, expanded_y, expanded_x + expanded_width, expanded_y + expanded_height],
                                outline=(255, 0, 0), width=3)

                print(f"生成椭圆形mask: 中心({center_x}, {center_y}) 轴长: {expanded_width//2}x{expanded_height//2}, 扩展: {expand_pixels}px")

            else:
                # 圆角矩形或其他形状：使用原来的圆润路径点方法
                center_x = cb_x + cb_width // 2
                center_y = cb_y + cb_height // 2

                # 向外扩展的像素数
                expand_pixels = 6  # 向外扩展6像素
                expanded_x = max(0, cb_x - expand_pixels)
                expanded_y = max(0, cb_y - expand_pixels)
                expanded_width = min(cb_width + 2 * expand_pixels, width - expanded_x)
                expanded_height = min(cb_height + 2 * expand_pixels, height - expanded_y)

                # 创建多个圆形路径点来覆盖扩展后的色块区域
                path_points = []
                brush_radius = min(expanded_width, expanded_height) // 10  # 笔刷半径

                # 生成覆盖扩展区域的路径点网格
                step_size = brush_radius // 2  # 步长，确保覆盖完整
                for y in range(expanded_y, expanded_y + expanded_height, step_size):
                    for x in range(expanded_x, expanded_x + expanded_width, step_size):
                        # 添加一些随机扰动，让路径更自然
                        noise_x = np.random.randint(-step_size//4, step_size//4)
                        noise_y = np.random.randint(-step_size//4, step_size//4)
                        path_points.append((x + noise_x, y + noise_y))

                # 在轨迹上画圆，确保不超出扩展区域边界
                for x, y in path_points:
                    # 确保圆不会超出扩展区域边界
                    left = max(x - brush_radius, expanded_x)
                    top = max(y - brush_radius, expanded_y)
                    right = min(x + brush_radius, expanded_x + expanded_width)
                    bottom = min(y + brush_radius, expanded_y + expanded_height)

                    # 使用椭圆代替圆，以适应可能的边界限制
                    if right > left and bottom > top:
                        cv2.ellipse(mask_np,
                                    center=((left + right) // 2, (top + bottom) // 2),
                                    axes=((right - left) // 2, (bottom - top) // 2),
                                    angle=0,
                                    startAngle=0,
                                    endAngle=360,
                                    color=255,
                                    thickness=-1)

                # 应用高斯模糊
                y1_safe = max(0, expanded_y)
                y2_safe = min(height, expanded_y + expanded_height)
                x1_safe = max(0, expanded_x)
                x2_safe = min(width, expanded_x + expanded_width)

                if y1_safe < y2_safe and x1_safe < x2_safe:
                    roi = mask_np[y1_safe:y2_safe, x1_safe:x2_safe]
                    if roi.size > 0:
                        blurred_roi = cv2.GaussianBlur(roi, (5, 5), 2)
                        mask_np[y1_safe:y2_safe, x1_safe:x2_safe] = blurred_roi

                # 在可视化图像上绘制矩形边框
                show_draw.rectangle([expanded_x, expanded_y, expanded_x + expanded_width, expanded_y + expanded_height],
                                  outline=(255, 0, 0), width=3)

                print(f"生成圆润矩形mask: ({expanded_x}, {expanded_y}) 尺寸: {expanded_width}x{expanded_height}, 扩展: {expand_pixels}px, 笔刷半径: {brush_radius}")
        else:
            # 如果不是色块mask，使用原来的逻辑（备用）
            region_x = region['x']
            region_y = region['y']
            region_width = region['width']
            region_height = region['height']

            # 绘制矩形mask
            mask_np[region_y - region_height:region_y, region_x:region_x + region_width] = 255

            # 在可视化图像上绘制红色边框
            show_draw.rectangle([region_x, region_y - region_height,
                               region_x + region_width, region_y],
                              outline=(255, 0, 0), width=2)

    # 将NumPy数组转回为PIL图像
    mask_image = Image.fromarray(mask_np, mode='L')

    return mask_image, show_img

def create_colorblock_visualization_image(positioned_lines, erasure_regions, colorblock_info, image_size):
    """
    创建彩色块专用的可视化图像，显示字符位置、边框和彩色块边界

    参数:
        positioned_lines: 带位置信息的文本行列表
        erasure_regions: 要抹除的区域列表
        colorblock_info: 彩色块信息
        image_size: 图像尺寸

    返回:
        visualization_image: 可视化图像
    """
    from render_font_proj import create_visualization_image
    from PIL import ImageDraw

    # 首先使用原脚本的可视化函数
    vis_image = create_visualization_image(positioned_lines, erasure_regions, None, image_size)

    # 在可视化图像上添加彩色块边界
    if colorblock_info:
        draw = ImageDraw.Draw(vis_image)
        cb_x = colorblock_info['x']
        cb_y = colorblock_info['y']
        cb_width = colorblock_info['width']
        cb_height = colorblock_info['height']

        # 绘制彩色块边界（蓝色虚线框）
        # 由于PIL不直接支持虚线，我们绘制多个短线段来模拟虚线效果
        dash_length = 10
        gap_length = 5

        # 绘制上边
        x = cb_x
        while x < cb_x + cb_width:
            end_x = min(x + dash_length, cb_x + cb_width)
            draw.line([(x, cb_y), (end_x, cb_y)], fill=(0, 0, 255), width=2)
            x += dash_length + gap_length

        # 绘制下边
        x = cb_x
        while x < cb_x + cb_width:
            end_x = min(x + dash_length, cb_x + cb_width)
            draw.line([(x, cb_y + cb_height), (end_x, cb_y + cb_height)], fill=(0, 0, 255), width=2)
            x += dash_length + gap_length

        # 绘制左边
        y = cb_y
        while y < cb_y + cb_height:
            end_y = min(y + dash_length, cb_y + cb_height)
            draw.line([(cb_x, y), (cb_x, end_y)], fill=(0, 0, 255), width=2)
            y += dash_length + gap_length

        # 绘制右边
        y = cb_y
        while y < cb_y + cb_height:
            end_y = min(y + dash_length, cb_y + cb_height)
            draw.line([(cb_x + cb_width, y), (cb_x + cb_width, end_y)], fill=(0, 0, 255), width=2)
            y += dash_length + gap_length

    return vis_image

def create_colorblock_combined_image(original_image, gt_image, mask_image, positioned_lines, erasure_regions, colorblock_info):
    """
    创建彩色块专用的拼接图像，包含彩色块边界可视化

    参数:
        original_image: 原始图像（PIL图像）
        gt_image: GT图像（PIL图像）
        mask_image: 掩码图像（PIL图像）
        positioned_lines: 带位置信息的文本行列表
        erasure_regions: 要抹除的区域列表
        colorblock_info: 彩色块信息

    返回:
        combined_image: 拼接后的图像（PIL图像）
    """
    from render_font_proj import overlay_mask_as_transparent_green
    from PIL import Image

    # 创建图1：原图+掩码（绿色透明覆盖）
    original_with_mask = overlay_mask_as_transparent_green(original_image, mask_image)

    # 创建图2：GT图+掩码（绿色透明覆盖）
    gt_with_mask = overlay_mask_as_transparent_green(gt_image, mask_image)

    # 创建图3：彩色块专用字符边框可视化图
    vis_image = create_colorblock_visualization_image(positioned_lines, erasure_regions, colorblock_info, original_image.size)

    # 确保三张图像大小相同
    width, height = original_image.size

    # 水平拼接图1、图2和图3
    combined_image = Image.new('RGB', (width * 3, height))
    combined_image.paste(original_with_mask, (0, 0))
    combined_image.paste(gt_with_mask, (width, 0))
    combined_image.paste(vis_image, (width * 2, 0))

    return combined_image

def select_colorblock_erasure_regions(positioned_lines, erasure_mode, colorblock_info, expand_boundary=False,
                                     multi_line_erasure=True, left_right_expand_boundary_ratio=0.3):
    """
    为彩色块定制的抹除区域选择函数
    修改后的版本：直接覆盖整个色块区域，模拟擦掉色块的场景

    参数:
        positioned_lines: 文字行信息（仅用于确保色块内有文字）
        erasure_mode: 抹除模式（此参数在全色块覆盖模式下被忽略）
        colorblock_info: 彩色块信息字典
        expand_boundary: 是否扩展边界（此参数在全色块覆盖模式下被忽略）
        multi_line_erasure: 是否多行抹除（此参数在全色块覆盖模式下被忽略）
        left_right_expand_boundary_ratio: 左右扩展比例（此参数在全色块覆盖模式下被忽略）

    返回:
        list: 包含整个色块区域的erasure_regions列表
    """
    erasure_regions = []

    if not positioned_lines or not colorblock_info:
        return erasure_regions

    # 获取彩色块边界
    cb_x = colorblock_info['x']
    cb_y = colorblock_info['y']
    cb_width = colorblock_info['width']
    cb_height = colorblock_info['height']

    # 检查色块内是否有文字（作为有效性验证）
    has_text_in_block = False
    for line in positioned_lines:
        if not line['chars']:
            continue

        for char in line['chars']:
            # 使用存储的实际渲染边界信息（如果有的话）
            if 'actual_left' in char:
                char_left = char['actual_left']
                char_right = char['actual_right']
                char_top = char['actual_top']
                char_bottom = char['actual_bottom']
            else:
                # 回退到原始计算方法
                char_left = char['x']
                char_right = char['x'] + char['width']
                char_top = char['y'] - char['height']
                char_bottom = char['y']

            # 如果有任何字符在色块内，标记为有文字
            if (char_left < cb_x + cb_width and char_right > cb_x and
                char_top < cb_y + cb_height and char_bottom > cb_y):
                has_text_in_block = True
                break

        if has_text_in_block:
            break

    # 如果色块内没有文字，返回空列表
    if not has_text_in_block:
        print("❌ 警告: 色块内没有文字，跳过生成掩码")
        print(f"  - 色块位置: ({cb_x}, {cb_y}), 尺寸: {cb_width}x{cb_height}")
        print(f"  - positioned_lines数量: {len(positioned_lines)}")
        for i, line in enumerate(positioned_lines):
            print(f"  - 第{i}行字符数: {len(line['chars'])}")
            if line['chars']:
                first_char = line['chars'][0]
                print(f"    首字符位置: ({first_char['x']}, {first_char['y']})")
        return erasure_regions

    # 创建一个覆盖整个色块的掩码区域
    # 修改：使用正确的坐标系统，y坐标使用顶部位置，并传递形状信息
    print(f"🔍 调试: 创建色块掩码区域")
    print(f"  - 色块信息: ({cb_x}, {cb_y}) 尺寸: {cb_width}x{cb_height}")
    print(f"  - 形状类型: {colorblock_info.get('shape_type', 'rounded_rect')}")

    mask_region = {
        'x': cb_x,
        'y': cb_y,  # 使用顶部y坐标
        'width': cb_width,
        'height': cb_height,
        'chars': [],  # 不再基于字符，但保留字段以兼容现有代码
        'is_full_colorblock': True,  # 标记这是一个覆盖整个色块的mask
        'shape': 'rectangle',  # 添加形状信息
        'shape_type': colorblock_info.get('shape_type', 'rounded_rect')  # 传递色块的实际形状类型
    }

    # 如果色块有字符，将第一行第一个字符添加到chars列表中以兼容现有代码
    for line in positioned_lines:
        if line['chars']:
            mask_region['chars'] = [line['chars'][0]]
            print(f"  - 添加兼容字符: '{line['chars'][0]['char']}'")
            break

    erasure_regions.append(mask_region)

    print(f"✅ 生成全色块掩码区域: ({cb_x}, {cb_y}) 尺寸: {cb_width}x{cb_height}")
    print(f"  - erasure_regions长度: {len(erasure_regions)}")

    return erasure_regions

def filter_chars_outside_ellipse(positioned_lines, ellipse_x, ellipse_y, ellipse_width, ellipse_height):
    """
    过滤掉椭圆外的字符

    参数:
        positioned_lines: 定位好的文字行
        ellipse_x, ellipse_y: 椭圆左上角坐标
        ellipse_width, ellipse_height: 椭圆宽度和高度

    返回:
        过滤后的positioned_lines
    """
    # 椭圆中心和半轴长度
    center_x = ellipse_x + ellipse_width / 2
    center_y = ellipse_y + ellipse_height / 2
    a = ellipse_width / 2  # 水平半轴
    b = ellipse_height / 2  # 垂直半轴

    # 安全边距（避免字符贴边）
    safety_margin = 10
    safe_a = max(a - safety_margin, a * 0.8)  # 至少保留80%
    safe_b = max(b - safety_margin, b * 0.8)

    print(f"椭圆中心: ({center_x:.1f}, {center_y:.1f}), 安全半轴: {safe_a:.1f}x{safe_b:.1f}")

    filtered_lines = []
    total_chars_before = 0
    total_chars_after = 0

    for line in positioned_lines:
        filtered_chars = []
        total_chars_before += len(line['chars'])

        for char in line['chars']:
            # 获取字符的实际渲染边界
            bbox = char['font'].getbbox(char['char'])
            bbox_left, bbox_top, bbox_right, bbox_bottom = bbox

            # 字符的实际渲染区域（绝对坐标）
            char_left = char['x'] + bbox_left
            char_right = char['x'] + bbox_right
            char_top = char['y'] + bbox_top
            char_bottom = char['y'] + bbox_bottom

            # 检查字符的四个角是否都在椭圆内
            corners = [
                (char_left, char_top),      # 左上角
                (char_right, char_top),     # 右上角
                (char_left, char_bottom),   # 左下角
                (char_right, char_bottom)   # 右下角
            ]

            # 检查所有角是否都在椭圆内
            all_corners_inside = True
            for corner_x, corner_y in corners:
                # 椭圆方程: ((x-cx)/a)^2 + ((y-cy)/b)^2 <= 1
                dx = (corner_x - center_x) / safe_a
                dy = (corner_y - center_y) / safe_b
                if dx*dx + dy*dy > 1.0:
                    all_corners_inside = False
                    break

            if all_corners_inside:
                filtered_chars.append(char)
            else:
                print(f"  移除椭圆外字符: '{char['char']}' at ({char['x']}, {char['y']})")

        # 只保留有字符的行
        if filtered_chars:
            filtered_line = line.copy()
            filtered_line['chars'] = filtered_chars
            filtered_lines.append(filtered_line)
            total_chars_after += len(filtered_chars)

    print(f"椭圆过滤统计: {total_chars_before} -> {total_chars_after} 字符")
    return filtered_lines


def generate_colorblock_erasure_dataset(output_dir, num_samples, current_idx, font_paths, font_sizes,
                                       texture_paths=None, dpi=300, erasure_mode='random', expand_boundary=False,
                                       multi_line_erasure=True, left_right_expand_boundary_ratio=0.3,
                                       line_spacing=10, char_spacing=5, num_lines=None, chars_per_line=None,
                                       use_image_background=False, image_paths=None, text_colors=None,
                                       img_name=None, target_size=(1487, 2105), colorblock_info=None):
    """
    生成带彩色块的抹除数据集
    直接实现彩色块版本，确保文字在彩色块内部

    返回:
        bool: 成功返回True，失败返回False
    """
    try:
        print(f"🔍 调试: 函数开始，参数检查")
        print(f"  - font_paths类型: {type(font_paths)}, 长度: {len(font_paths) if font_paths else 0}")
        print(f"  - font_sizes类型: {type(font_sizes)}, 长度: {len(font_sizes) if font_sizes else 0}")
        print(f"  - num_lines: {num_lines}")
        print(f"  - chars_per_line: {chars_per_line}")

        # 创建输出目录
        for folder in ['im', 'mask', 'gt', 'show']:
            os.makedirs(os.path.join(output_dir, folder), exist_ok=True)

        # 如果传入的字体参数为空，需要在函数内部生成
        if not font_paths or not font_sizes:
            print("⚠️  检测到空的字体参数，需要在函数内部生成字体和文字")
            # 这里需要实现字体生成逻辑
            # 暂时返回False，避免错误
            print("❌ 暂时不支持空字体参数，请传入有效的字体路径和大小")
            return False

        # 确保行数与字体列表长度一致
        if num_lines is None:
            num_lines = len(font_paths)
        else:
            num_lines = min(num_lines, len(font_paths))

        print(f"✅ 最终确定行数: {num_lines}")

        # 加载所有字体 - 添加调试输出
        print(f"调试: num_lines={num_lines}, font_paths长度={len(font_paths)}, font_sizes长度={len(font_sizes)}")

        fonts = []
        for i in range(num_lines):
            try:
                print(f"调试: 处理第{i}行字体")
                if i >= len(font_paths):
                    print(f"❌ 错误: font_paths索引{i}超出范围，长度为{len(font_paths)}")
                    return False
                if i >= len(font_sizes):
                    print(f"❌ 错误: font_sizes索引{i}超出范围，长度为{len(font_sizes)}")
                    return False

                font_path = font_paths[i][1]
                font_size = font_sizes[i]
                font = load_font_from_tif(font_path, font_size)
                fonts.append([font_paths[i][0], font])
                print(f"✅ 成功加载第{i}行字体: {font_paths[i][0]}")
            except Exception as e:
                print(f"❌ 加载第{i}行字体失败: {e}")
                return False

        # 生成样本
        if use_image_background and image_paths:
            # 使用图片背景
            try:
                # 随机选择一张图片
                image_path = random.choice(image_paths) if isinstance(image_paths, list) else image_paths
                if not os.path.exists(image_path):
                    print(f"警告: 图片路径不存在: {image_path}")
                    return False

                # 加载图片
                blank_image = Image.open(image_path)
                # 使用智能缩放
                blank_image = resize_image_with_aspect_ratio(blank_image, target_size)
                image_width, image_height = blank_image.size

                # 如果图片太小，跳过该样本
                if image_width < 100 or image_height < 100:
                    print(f"警告: 图片尺寸太小: {image_path}")
                    return False
            except Exception as e:
                print(f"警告: 加载图片失败: {image_path}, 错误: {e}")
                return False
        else:
            # 创建空白A4图像
            blank_image = create_blank_a4(dpi=dpi)
            blank_image = resize_image_with_aspect_ratio(blank_image, target_size)
            image_width, image_height = blank_image.size

        # 如果不是 RGB 模式，则转换为 RGB
        if blank_image.mode != 'RGB':
            blank_image = blank_image.convert('RGB')

        # 修改：新的逻辑 - 先生成文字，再根据文字边界生成色块
        print("🔄 开始新的生成流程：先生成文字，再生成色块")

        # 获取当前图像尺寸
        current_width, current_height = blank_image.size
        print(f"图像尺寸: {current_width}x{current_height}")

        # 决定文字边界策略：80%紧贴模式，20%宽松模式
        use_tight_fit = random.random() < 0.8  # 80%概率使用紧贴模式
        fit_mode = "紧贴模式" if use_tight_fit else "宽松模式"
        print(f"边界策略: {fit_mode} (紧贴概率: 80%)")

        # 新的可靠逻辑：先确定可用区域，再生成适合的文字和色块
        print("🔄 使用新的可靠生成逻辑")

        # 1. 确定图像可用区域（留边距）
        image_margin = 50  # 图像边距
        available_width = current_width - 2 * image_margin
        available_height = current_height - 2 * image_margin

        print(f"图像尺寸: {current_width}x{current_height}")
        print(f"可用区域: {available_width}x{available_height}")

        # 2. 确定色块边距
        if use_tight_fit:
            colorblock_margin = 15  # 紧贴模式边距
            fit_type = "紧贴模式"
        else:
            colorblock_margin = 25  # 宽松模式边距
            fit_type = "宽松模式"

        # 3. 计算文字可用区域（色块内部）
        text_available_width = available_width - 2 * colorblock_margin
        text_available_height = available_height - 2 * colorblock_margin

        print(f"边界策略: {fit_type}, 色块边距: {colorblock_margin}px")
        print(f"文字可用区域: {text_available_width}x{text_available_height}")

        # 4. 动态调整文字参数以适应可用区域
        max_attempts = 10
        for attempt in range(max_attempts):
            # 随机生成文字参数
            num_lines = random.randint(2, 4)
            chars_per_line = random.randint(3, 6)

            # 估算合适的字体大小
            estimated_char_width = text_available_width / chars_per_line * 0.8
            estimated_char_height = text_available_height / num_lines * 0.8
            estimated_font_size = min(estimated_char_width, estimated_char_height)
            estimated_font_size = max(20, min(estimated_font_size, 200))  # 限制范围

            # 生成测试文字
            test_lines = []
            for i in range(num_lines):
                if i < len(fonts):
                    font_name, original_font = fonts[i]
                else:
                    font_name, original_font = fonts[0]

                # 使用估算的字体大小创建新字体
                font_path = font_paths[i % len(font_paths)][1]
                test_font = load_font_from_tif(font_path, int(estimated_font_size))

                line_chars = []
                for j in range(chars_per_line):
                    char = random.choice("的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞")
                    char_width, char_height = get_text_size(char, test_font)
                    line_chars.append({
                        'char': char,
                        'width': char_width,
                        'height': char_height,
                        'font': test_font,
                        'index': j
                    })

                test_lines.append({
                    'chars': line_chars,
                    'font': test_font,
                    'index': i
                })

            # 计算文字实际渲染尺寸（使用bbox）
            total_text_width = 0
            total_text_height = 0

            for line in test_lines:
                if line['chars']:
                    # 计算行的实际渲染宽度
                    line_bbox_width = 0
                    line_bbox_height = 0

                    for char in line['chars']:
                        # 使用字体的getbbox获取实际渲染边界
                        bbox = char['font'].getbbox(char['char'])
                        bbox_left, bbox_top, bbox_right, bbox_bottom = bbox
                        char_render_width = bbox_right - bbox_left
                        char_render_height = bbox_bottom - bbox_top

                        line_bbox_width += char_render_width
                        line_bbox_height = max(line_bbox_height, char_render_height)

                    # 加上字符间距
                    if len(line['chars']) > 1:
                        line_bbox_width += (len(line['chars']) - 1) * char_spacing

                    total_text_width = max(total_text_width, line_bbox_width)
                    total_text_height += line_bbox_height

            # 加上行间距
            if len(test_lines) > 1:
                total_text_height += (len(test_lines) - 1) * line_spacing

            print(f"尝试 {attempt + 1}: 字号{int(estimated_font_size)}, {num_lines}行x{chars_per_line}字符, 文字尺寸: {total_text_width:.1f}x{total_text_height:.1f}")

            # 检查是否适合
            if total_text_width <= text_available_width and total_text_height <= text_available_height:
                print(f"✅ 找到合适的文字配置")
                lines = test_lines
                break
        else:
            print("❌ 无法找到合适的文字配置，跳过此样本")
            return False

        # 5. 在可用区域内随机定位文字
        # 计算文字可以放置的范围
        text_x_range = text_available_width - total_text_width
        text_y_range = text_available_height - total_text_height

        if text_x_range < 0 or text_y_range < 0:
            print("❌ 文字仍然太大，跳过此样本")
            return False

        # 随机选择文字位置（相对于可用区域）
        text_offset_in_available_x = random.uniform(0, text_x_range)
        text_offset_in_available_y = random.uniform(0, text_y_range)

        # 转换为绝对坐标
        text_abs_x = int(image_margin + colorblock_margin + text_offset_in_available_x)
        text_abs_y = int(image_margin + colorblock_margin + text_offset_in_available_y)

        # 6. 生成刚好包围文字的色块
        cb_x = int(image_margin + text_offset_in_available_x)
        cb_y = int(image_margin + text_offset_in_available_y)
        cb_width = int(total_text_width + 2 * colorblock_margin)
        cb_height = int(total_text_height + 2 * colorblock_margin)

        # 随机选择色块形状和颜色
        shape_type = random.choices(
            list(COLORBLOCK_SHAPE_PROB.keys()),
            weights=list(COLORBLOCK_SHAPE_PROB.values())
        )[0]
        cb_color = generate_colorblock_color()

        print(f"色块形状: {shape_type}, 颜色: {cb_color}")
        print(f"文字绝对位置: ({text_abs_x:.1f}, {text_abs_y:.1f})")
        print(f"色块位置: ({cb_x}, {cb_y}), 尺寸: {cb_width}x{cb_height}")

        # 创建色块形状mask
        shape_mask = Image.new('L', (cb_width, cb_height), 0)
        draw = ImageDraw.Draw(shape_mask)

        if shape_type == 'rounded_rect':
            corner_radius = min(cb_width, cb_height) // 8
            draw.rounded_rectangle([0, 0, cb_width-1, cb_height-1], radius=corner_radius, fill=255)
        elif shape_type == 'ellipse':
            draw.ellipse([0, 0, cb_width-1, cb_height-1], fill=255)

        # 7. 在背景图上添加彩色块
        original_image = blank_image.copy()
        colorblock_img = Image.new('RGB', (cb_width, cb_height), cb_color)
        original_image.paste(colorblock_img, (cb_x, cb_y), shape_mask)

        print(f"✅ 色块已添加到图像: 位置({cb_x}, {cb_y}), 尺寸{cb_width}x{cb_height}")

        # 8. 精确定位文字（使用bbox确保准确性）
        positioned_lines = []
        current_y = text_abs_y

        for line_idx, line in enumerate(lines):
            if not line['chars']:
                continue

            # 计算行的实际渲染尺寸
            line_bbox_width = 0
            line_bbox_height = 0
            char_bbox_widths = []

            for char in line['chars']:
                bbox = char['font'].getbbox(char['char'])
                bbox_left, bbox_top, bbox_right, bbox_bottom = bbox
                char_render_width = bbox_right - bbox_left
                char_render_height = bbox_bottom - bbox_top

                char_bbox_widths.append(char_render_width)
                line_bbox_width += char_render_width
                line_bbox_height = max(line_bbox_height, char_render_height)

            # 加上字符间距
            if len(line['chars']) > 1:
                line_bbox_width += (len(line['chars']) - 1) * char_spacing

            # 水平居中（基于实际渲染宽度）
            start_x = int(text_abs_x + (total_text_width - line_bbox_width) / 2)

            positioned_chars = []
            current_x = start_x

            for char_idx, char in enumerate(line['chars']):
                # 计算基线位置（考虑字符的实际渲染高度）
                bbox = char['font'].getbbox(char['char'])
                bbox_left, bbox_top, bbox_right, bbox_bottom = bbox

                # 基线位置 = 当前Y + 行高度 - bbox_bottom（因为bbox_bottom是相对于基线的）
                baseline_y = int(current_y + line_bbox_height - bbox_bottom)

                positioned_char = {
                    'char': char['char'],
                    'x': int(current_x),
                    'y': baseline_y,  # 正确的基线位置
                    'width': char['width'],  # 保留原始宽度用于兼容性
                    'height': char['height'], # 保留原始高度用于兼容性
                    'font': char['font'],
                    'color': char.get('color', (0, 0, 0)),
                    'index': char_idx
                }
                positioned_chars.append(positioned_char)
                current_x += char_bbox_widths[char_idx] + char_spacing

            positioned_line = {
                'chars': positioned_chars,
                'height': line_bbox_height,
                'y': int(current_y),
                'index': line_idx,
                'font': line.get('font')
            }
            positioned_lines.append(positioned_line)
            current_y += line_bbox_height + line_spacing

        print(f"✅ 文字精确定位完成: {len(positioned_lines)}行")

        # 特殊处理：如果是椭圆形色块，移除椭圆外的字符
        if shape_type == 'ellipse':
            print(f"🔄 椭圆形色块特殊处理：移除椭圆外的字符")
            positioned_lines = filter_chars_outside_ellipse(positioned_lines, cb_x, cb_y, cb_width, cb_height)

            # 检查过滤后是否还有足够的字符
            remaining_chars = sum(len(line['chars']) for line in positioned_lines)
            if remaining_chars < 3:  # 至少需要3个字符
                print(f"❌ 椭圆过滤后字符太少 ({remaining_chars} < 3)，跳过此样本")
                return False

            print(f"✅ 椭圆过滤完成，剩余 {remaining_chars} 个字符")

        # 9. 更新彩色块信息用于后续处理
        updated_colorblock_info = {
            'x': cb_x, 'y': cb_y, 'width': cb_width, 'height': cb_height,
            'color': cb_color, 'shape_mask': shape_mask,
            'shape_type': shape_type
        }

        # 生成文字颜色（如果没有提供）
        if text_colors is None:
            text_colors = generate_text_colors(len(positioned_lines), cb_color)
            print(f"✅ 生成文字颜色: {text_colors}")

        # 在彩色块上渲染文字 - 添加调试信息
        print(f"🔍 调试: 开始渲染文字到图像")
        print(f"  - original_image类型: {type(original_image)}")
        print(f"  - positioned_lines长度: {len(positioned_lines)}")
        print(f"  - text_colors: {text_colors}")

        original_image = render_text(original_image, positioned_lines, None, text_colors=text_colors)

        print(f"🔍 调试: 文字渲染结果")
        print(f"  - original_image类型: {type(original_image)}")
        print(f"  - original_image是否为None: {original_image is None}")

        # 最终验证：检查所有文字是否都在色块内（使用正确的坐标系统）
        print(f"🔍 最终验证: 检查文字是否在色块内")
        all_chars_inside = True
        for line_idx, line in enumerate(positioned_lines):
            for char_idx, char in enumerate(line['chars']):
                # 获取字符的字体和基线位置
                char_font = char['font']
                baseline_x = char['x']
                baseline_y = char['y']

                # 使用字体的getbbox方法获取实际渲染边界
                bbox = char_font.getbbox(char['char'])
                bbox_left, bbox_top, bbox_right, bbox_bottom = bbox

                # 计算字符的实际渲染边界（绝对坐标）
                char_left = baseline_x + bbox_left
                char_right = baseline_x + bbox_right
                char_top = baseline_y + bbox_top
                char_bottom = baseline_y + bbox_bottom

                # 检查字符是否在色块边界内（留5像素安全边距）
                safety_margin = 5
                cb_left = cb_x + safety_margin
                cb_right = cb_x + cb_width - safety_margin
                cb_top = cb_y + safety_margin
                cb_bottom = cb_y + cb_height - safety_margin

                if (char_left < cb_left or char_right > cb_right or
                    char_top < cb_top or char_bottom > cb_bottom):
                    print(f"❌ 字符 '{char['char']}' 超出色块边界!")
                    print(f"   字符实际渲染位置: ({char_left:.1f}, {char_top:.1f}) -> ({char_right:.1f}, {char_bottom:.1f})")
                    print(f"   色块安全边界: ({cb_left}, {cb_top}) -> ({cb_right}, {cb_bottom})")
                    print(f"   基线位置: ({baseline_x}, {baseline_y})")
                    print(f"   字符bbox: {bbox}")
                    all_chars_inside = False

        if all_chars_inside:
            print(f"✅ 验证通过: 所有文字都在色块内")
        else:
            print(f"❌ 验证失败: 有文字超出色块边界，跳过此样本")
            return False

        if original_image is None:
            print("❌ 原图渲染文字出错")
            return False

        # 选择抹除区域（使用彩色块专用函数） - 添加调试信息
        print(f"🔍 调试: 开始选择抹除区域")
        print(f"  - positioned_lines长度: {len(positioned_lines) if positioned_lines else 0}")
        print(f"  - updated_colorblock_info: {updated_colorblock_info is not None}")

        erasure_regions = select_colorblock_erasure_regions(
            positioned_lines, erasure_mode, updated_colorblock_info,
            expand_boundary, multi_line_erasure, left_right_expand_boundary_ratio
        )

        print(f"🔍 调试: 抹除区域选择结果")
        print(f"  - erasure_regions类型: {type(erasure_regions)}")
        print(f"  - erasure_regions长度: {len(erasure_regions) if erasure_regions else 0}")

        # 调试信息：输出mask区域信息，验证修复效果
        if erasure_regions:
            print(f"生成了 {len(erasure_regions)} 个mask区域:")
            for i, region in enumerate(erasure_regions):
                print(f"  区域{i}: x={region['x']}, y={region['y']}, w={region['width']}, h={region['height']}")
                if region['chars']:
                    first_char = region['chars'][0]
                    print(f"    首字符: '{first_char['char']}' at ({first_char['x']}, {first_char['y']})")
        else:
            print("警告: 未生成任何mask区域")

        # 修改：创建GT图像直接使用原图（模拟擦掉色块的场景）
        # GT图像就是原始背景图，不包含任何彩色块和文字
        gt_image = blank_image.copy()
        print("✅ GT图像使用原始背景（模拟擦掉色块后的效果）")

        # 创建掩码图像（基于字符轮廓，与原脚本一致） - 添加调试信息
        print(f"🔍 调试: 开始创建mask图像")
        print(f"  - 图像尺寸: {image_width}x{image_height}")
        print(f"  - original_image: {original_image is not None}")

        mask_image, _ = create_colorblock_mask_image((image_width, image_height), erasure_regions, original_image)

        print(f"🔍 调试: mask图像创建结果")
        print(f"  - mask_image类型: {type(mask_image)}")
        print(f"  - mask_image是否为None: {mask_image is None}")

        if mask_image is None:
            print("❌ 错误: mask_image为None，这是导致后续错误的原因")
            return False

        # 调试信息：验证mask区域与字符位置的对齐（修复后）
        if erasure_regions:
            print(f"✅ 生成了 {len(erasure_regions)} 个mask区域（已修复坐标）:")
            for i, region in enumerate(erasure_regions):
                print(f"  区域{i}: x={region['x']}, y={region['y']}, w={region['width']}, h={region['height']}")
                if region['chars']:
                    first_char = region['chars'][0]
                    print(f"    首字符: '{first_char['char']}' 基线位置=({first_char['x']}, {first_char['y']})")

                    # 获取字符的bbox信息进行详细分析
                    if positioned_lines and 'font' in positioned_lines[0]:
                        font = positioned_lines[0]['font']
                        if font:
                            bbox = font.getbbox(first_char['char'])
                            actual_top = first_char['y'] + bbox[1]
                            actual_bottom = first_char['y'] + bbox[3]
                            print(f"    字符bbox: {bbox}")
                            print(f"    实际渲染范围: 顶部={actual_top}, 底部={actual_bottom}")
                            print(f"    mask覆盖范围: 顶部={region['y'] - region['height']}, 底部={region['y']}")
                            print(f"    ✅ 对齐检查: mask顶部应该≈实际顶部 ({region['y'] - region['height']} ≈ {actual_top})")

        print(f"GT图像处理完成，显示擦除后的彩色块效果")

        # 创建拼接图像用于可视化（使用彩色块专用函数）
        combined_image = create_colorblock_combined_image(original_image, gt_image, mask_image, positioned_lines, erasure_regions, updated_colorblock_info)

        # 保存图像
        if img_name:
            name = os.path.splitext(os.path.basename(image_path))[0]
            original_path = os.path.join(output_dir, 'im', name + "_" + str(current_idx) + ".png")
            mask_path = os.path.join(output_dir, 'mask', name + "_" + str(current_idx) + ".png")
            gt_path = os.path.join(output_dir, 'gt', name + "_" + str(current_idx) + ".png")
            show_path = os.path.join(output_dir, 'show', name + "_" + str(current_idx) + ".png")
        else:
            original_path = os.path.join(output_dir, 'im', f'sample_{current_idx:05d}.png')
            mask_path = os.path.join(output_dir, 'mask', f'sample_{current_idx:05d}.png')
            gt_path = os.path.join(output_dir, 'gt', f'sample_{current_idx:05d}.png')
            show_path = os.path.join(output_dir, 'show', f'sample_{current_idx:05d}.png')

        if os.path.exists(original_path):
            print(f"{original_path} 已存在，跳过")
            return False

        original_image.save(original_path)
        mask_image.save(mask_path)
        gt_image.save(gt_path)
        combined_image.save(show_path)

        return True

    except Exception as e:
        print(f"生成彩色块数据集时出错 (current_idx: {current_idx}): {e}")
        return False



def generate_samples_parallel(image_folder, output_folder, total_samples=10000, num_processes=4):
    """并行生成样本，复用原脚本结构"""
    print(f"开始处理文件夹: {image_folder}")

    # 创建输出目录
    for folder in ['im', 'mask', 'gt', 'show']:
        os.makedirs(os.path.join(output_folder, folder), exist_ok=True)

    # 收集图片文件
    image_files = collect_image_files(image_folder)
    print(f"收集到 {len(image_files)} 个图片文件")

    if not image_files:
        print(f"警告：{image_folder} 中没有图片文件")
        return

def check_paths():
    """检查必要的路径是否存在"""
    # 检查字体路径
    if not os.path.exists(FONT_PATH):
        print(f"❌ 错误：字体文件夹 {FONT_PATH} 不存在")
        print("请确保字体文件夹存在，或修改 FONT_PATH 配置")
        return False

    # 检查图片路径
    if not os.path.exists(IMAGE_PATH):
        print(f"❌ 错误：背景图片文件夹 {IMAGE_PATH} 不存在")
        print("请确保背景图片文件夹存在，或修改 IMAGE_PATH 配置")
        return False

    # 创建输出目录
    try:
        os.makedirs(OUTPUT_PATH, exist_ok=True)
        # 创建输出子目录
        for folder in ['im', 'mask', 'gt', 'show']:
            os.makedirs(os.path.join(OUTPUT_PATH, folder), exist_ok=True)
        print(f"✅ 输出目录已创建：{OUTPUT_PATH}")
    except Exception as e:
        print(f"❌ 错误：无法创建输出目录 {OUTPUT_PATH}: {e}")
        return False

    return True

def generate_samples_parallel(image_folder, output_folder, total_samples, num_processes=6):
    """
    并行生成样本的主函数

    参数:
        image_folder: 图片文件夹路径
        output_folder: 输出文件夹路径
        total_samples: 总样本数
        num_processes: 进程数
    """
    print(f"开始并行生成样本：")
    print(f"  图片文件夹: {image_folder}")
    print(f"  输出文件夹: {output_folder}")
    print(f"  总样本数: {total_samples}")
    print(f"  进程数: {num_processes}")

    # 收集图片文件
    image_files = collect_image_files(image_folder)
    if not image_files:
        print(f"❌ 错误：在 {image_folder} 中未找到任何图片文件")
        return

    print(f"收集到 {len(image_files)} 个图片文件")

    # 显示前几个图片文件路径用于调试
    print("前5个图片文件:")
    for i, img_path in enumerate(image_files[:5]):
        print(f"  {i+1}: {img_path}")

    # 初始化共享状态
    shared = init_shared_state(image_files)
    print(f"共享状态初始化完成，图片总数: {shared.total_images}")
    # 计算每个进程应生成的样本数，确保总数准确
    base_samples_per_process = total_samples // num_processes
    remainder = total_samples % num_processes

    print(f"样本分配: 总共{total_samples}个样本，{num_processes}个进程")
    print(f"基础分配: 每个进程{base_samples_per_process}个样本")
    if remainder > 0:
        print(f"余数分配: 前{remainder}个进程额外生成1个样本")

    # 创建进程池，为每个进程分配准确的样本数
    with Pool(processes=num_processes) as pool:
        args_list = []
        for i in range(num_processes):
            # 前remainder个进程多生成1个样本
            samples_for_this_process = base_samples_per_process + (1 if i < remainder else 0)
            args_list.append((output_folder, shared, samples_for_this_process, i))
            print(f"进程{i}: 分配{samples_for_this_process}个样本")

        pool.map(generate_samples_task, args_list)
        # 等待所有进程完成
        pool.close()
        pool.join()

    # 输出统计信息
    actual_generated = shared.actual_generated
    print(f"{image_folder} 处理完成")
    print(f"预分配样本数: {total_samples}")
    print(f"实际生成样本数: {actual_generated}")
    if actual_generated < total_samples:
        print(f"⚠️  有 {total_samples - actual_generated} 个样本生成失败被跳过")
    print(f"图片使用统计：")
    print(f"- 顺序使用图片数量: {min(shared.image_index, shared.total_images)}")
    print(f"- 随机重复使用次数: {max(0, total_samples - shared.total_images)}")

    return actual_generated  # 返回实际生成的数量

def main():
    """主函数，复用原脚本结构"""
    print("=" * 60)
    print("自然场景+彩色块文字消除数据集生成脚本")
    print("=" * 60)

    # 检查路径配置
    if not check_paths():
        print("请检查路径配置后重新运行脚本")
        return

    # 检查图片子文件夹
    image_subfolders = []
    if os.path.isdir(IMAGE_PATH):
        # 如果IMAGE_PATH直接包含图片文件，直接使用
        image_files = [f for f in os.listdir(IMAGE_PATH)
                      if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
        if image_files:
            image_subfolders = ['.']  # 使用当前目录
        else:
            # 查找子文件夹
            for item in os.listdir(IMAGE_PATH):
                item_path = os.path.join(IMAGE_PATH, item)
                if os.path.isdir(item_path):
                    # 检查子文件夹是否包含图片
                    sub_images = [f for f in os.listdir(item_path)
                                 if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
                    if sub_images:
                        image_subfolders.append(item)

    if not image_subfolders:
        print(f"❌ 错误：在 {IMAGE_PATH} 中未找到任何图片文件")
        return

    print(f"发现图片文件夹：{image_subfolders}")

    # 🔥 关键修复：限制总样本数量，而不是每个文件夹都生成SAMPLES_PER_FOLDER个
    total_folders = len(image_subfolders)
    if total_folders > 1:
        print(f"⚠️  发现{total_folders}个图片文件夹，将平均分配样本数量")
        samples_per_folder = max(1, SAMPLES_PER_FOLDER // total_folders)
        remaining_samples = SAMPLES_PER_FOLDER % total_folders
        print(f"每个文件夹基础分配: {samples_per_folder}个样本")
        if remaining_samples > 0:
            print(f"前{remaining_samples}个文件夹额外分配1个样本")
    else:
        samples_per_folder = SAMPLES_PER_FOLDER
        remaining_samples = 0

    total_generated = 0

    # 遍历图片文件夹
    for folder_idx, image_subfolder in enumerate(image_subfolders):
        if image_subfolder == '.':
            input_path = IMAGE_PATH
            print(f"开始处理文件夹：{input_path}")
        else:
            input_path = os.path.join(IMAGE_PATH, image_subfolder)
            print(f"开始处理文件夹：{input_path}")

        # 计算这个文件夹应该生成的样本数
        if total_folders > 1:
            folder_samples = samples_per_folder + (1 if folder_idx < remaining_samples else 0)
            print(f"文件夹 {folder_idx + 1}/{total_folders}: 分配{folder_samples}个样本")
        else:
            folder_samples = SAMPLES_PER_FOLDER

        actual_generated = generate_samples_parallel(
            image_folder=input_path,
            output_folder=OUTPUT_PATH,
            total_samples=folder_samples,
            num_processes=6
        )

        total_generated += actual_generated
        print(f"文件夹处理完成，累计生成: {total_generated}/{SAMPLES_PER_FOLDER} 个样本")

    # 🔥 补丁：如果生成数量不足，继续生成直到达到目标
    max_retry_rounds = 3  # 最多重试3轮，避免无限循环
    retry_round = 0

    while total_generated < SAMPLES_PER_FOLDER and retry_round < max_retry_rounds:
        retry_round += 1
        shortage = SAMPLES_PER_FOLDER - total_generated
        print("=" * 60)
        print(f"🔄 第{retry_round}轮补充生成")
        print(f"当前已生成: {total_generated}/{SAMPLES_PER_FOLDER}")
        print(f"需要补充: {shortage} 个样本")
        print("=" * 60)

        # 重新分配补充任务到各个文件夹
        if total_folders > 1:
            base_shortage_per_folder = max(1, shortage // total_folders)
            remaining_shortage = shortage % total_folders
            print(f"每个文件夹基础补充: {base_shortage_per_folder}个样本")
            if remaining_shortage > 0:
                print(f"前{remaining_shortage}个文件夹额外补充1个样本")
        else:
            base_shortage_per_folder = shortage
            remaining_shortage = 0

        # 为每个文件夹补充生成样本
        for folder_idx, image_subfolder in enumerate(image_subfolders):
            if image_subfolder == '.':
                input_path = IMAGE_PATH
            else:
                input_path = os.path.join(IMAGE_PATH, image_subfolder)

            # 计算这个文件夹需要补充的样本数
            if total_folders > 1:
                folder_shortage = base_shortage_per_folder + (1 if folder_idx < remaining_shortage else 0)
                if folder_shortage <= 0:
                    continue
                print(f"文件夹 {folder_idx + 1}/{total_folders}: 补充{folder_shortage}个样本")
            else:
                folder_shortage = shortage

            additional_generated = generate_samples_parallel(
                image_folder=input_path,
                output_folder=OUTPUT_PATH,
                total_samples=folder_shortage,
                num_processes=6
            )

            total_generated += additional_generated
            print(f"文件夹补充完成，累计生成: {total_generated}/{SAMPLES_PER_FOLDER} 个样本")

            # 如果已经达到目标，提前退出
            if total_generated >= SAMPLES_PER_FOLDER:
                break

    print("=" * 60)
    print("数据集生成完成！")
    print(f"总共生成样本数: {total_generated}")
    print(f"目标样本数: {SAMPLES_PER_FOLDER}")
    if total_generated >= SAMPLES_PER_FOLDER:
        print("✅ 成功达到目标样本数量")
    else:
        print(f"⚠️  未能达到目标数量，可能由于样本生成失败率过高")
        print(f"建议：检查字体文件、图片质量或调整生成参数")
    print(f"输出目录：{OUTPUT_PATH}")
    print("=" * 60)

if __name__ == "__main__":
    main()
