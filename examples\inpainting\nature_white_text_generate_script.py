#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自然场景文字消除数据集生成脚本

技术要点和设计规格：

1. 文字生成方式：
   - 直接在自然场景图片上放置文字，无彩色块背景
   - 文字颜色：90%白色文字，10%彩色文字（高饱和度高亮度）
   - 文字行数：2-6行随机
   - 每行字符数：3-8个字符随机

2. 文字颜色策略：
   - 白色文字：RGB(255,255,255)，确保在大多数自然场景中可见
   - 彩色文字：HSV随机生成，饱和度S[0.7,1.0]，明度V[0.8,1.0]，确保高对比度

3. 文字尺寸和位置：
   - 动态字号：基于图片短边的8%-25%
   - 位置：在图像边界内随机放置，保持50px安全边距
   - 自适应尺寸：确保在不同分辨率下视觉一致性

4. Mask生成要求：
   - 覆盖范围：只覆盖文字区域
   - 形状：遵照nature_generate_script.py的不规则文字轮廓
   - 边界限制：严格保证不超出图像边界

5. 输出要求：
   - GT图像：被mask覆盖区域显示原始自然场景背景
   - 其他区域与输入图像保持一致

6. Mask策略多样性：
   - 30%全部涂抹：整行文字全覆盖
   - 40%部分涂抹：随机选择30%-70%的字符
   - 30%单个涂抹：只涂抹1个字符（最难样本）

基于nature_generate_script.py和nature_colorblock_generate_script.py的结构，
复用其字体、字号、mask生成方式，但移除彩色块相关逻辑
"""

import os
import random
import shutil
import math
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from render_font_proj import (
    load_font_from_tif, resize_image_with_aspect_ratio,
    create_blank_a4, generate_text_lines, render_text,
    create_combined_image, create_mask_image, calculate_text_positions
)
from multiprocessing import Manager, Pool
import colorsys

def init_shared_state(image_files):
    """初始化共享状态，复用原脚本逻辑"""
    manager = Manager()
    shared = manager.Namespace()
    shared.image_index = 0
    shared.image_path_cache = []
    shared.image_files = image_files
    shared.total_images = len(image_files)
    shared.lock = manager.Lock()
    # 添加实际生成数量计数器
    shared.actual_generated = 0
    return shared

# 配置参数 - 基于原脚本调整
# 获取脚本所在目录，然后构建相对于项目根目录的路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))  # 向上两级到项目根目录

FONT_PATH = os.path.join(project_root, "resource", "font")  # 字体文件夹路径
IMAGE_PATH = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/nature/"  # 背景图片文件夹路径
OUTPUT_PATH = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/nature_white_text_dataset"  # 输出文件夹路径

# 如果需要使用相对路径，请根据实际情况修改以下路径：
# IMAGE_PATH = os.path.join(project_root, "resource", "nature_images")
# OUTPUT_PATH = os.path.join(project_root, "output", "nature_white_text_dataset")

# 字体类型及其抽取概率（复用原脚本配置）
FONT_PROB = {
    "TraditionalChinese": 0,  # 暂时禁用，因为字体文件不足
    "SimplifiedChinese": 6,   # 增加简体中文的权重
    "Number": 3,
    "Letter": 2,
    "Punctuation": 2
}

# 样本生成参数
SAMPLES_PER_FOLDER = 3400  # 每个文件夹需要生成的样本数
# 字号基于图片短边的百分比
FONT_SIZE_RATIO_RANGE = (0.08, 0.25)  # 字号占图片短边的8%-25%
LINE_SPACING_RANGE = (10, 30)  # 行间距范围
CHAR_SPACING_RANGE = (3, 8)  # 字符间距范围

# 文字颜色配置
WHITE_TEXT_PROB = 0.9  # 90%白色文字概率
COLORED_TEXT_PROB = 0.1  # 10%彩色文字概率

# 文字配置
NUM_LINES_RANGE = (2, 6)  # 文字行数范围：2-6行
CHARS_PER_LINE_RANGE = (3, 8)  # 每行字符数范围：3-8个字符

# Mask策略多样性配置
MASK_STRATEGY_PROB = {
    'whole_line': 0.3,      # 30%全部涂抹
    'partial_chars': 0.4,   # 40%部分涂抹
    'single_char': 0.3      # 30%单个涂抹
}

# 部分涂抹时的字符选择比例
PARTIAL_CHARS_RATIO_RANGE = (0.3, 0.7)  # 30%-70%的字符

def collect_font_paths():
    """收集所有字体文件路径，复用原脚本逻辑"""
    font_paths = {
        "TraditionalChinese": [],
        "SimplifiedChinese": [],
        "Number": [],
        "Letter": [],
        "Punctuation": []
    }

    # 收集 TraditionalChinese 和 SimplifiedChinese 的字体
    for font_type in ["TraditionalChinese", "SimplifiedChinese"]:
        font_dir = os.path.join(FONT_PATH, font_type)
        if not os.path.exists(font_dir):
            continue

        for root, _, files in os.walk(font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.otf')):
                    font_paths[font_type].append(os.path.join(root, file))

    # 收集 Other 文件夹下的字体，供 Number 和 Letter 使用
    other_font_dir = os.path.join(FONT_PATH, "Other")
    if os.path.exists(other_font_dir):
        for root, _, files in os.walk(other_font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.otf')):
                    font_file = os.path.join(root, file)
                    font_paths["Number"].append(font_file)
                    font_paths["Letter"].append(font_file)
                    font_paths["Punctuation"].append(font_file)
    return font_paths

def sample_fonts(font_paths, num_samples):
    """根据概率抽样字体，复用原脚本逻辑，智能处理字体不足的情况"""
    # 只考虑有可用字体的类型
    available_font_types = {font_type: prob for font_type, prob in FONT_PROB.items()
                           if font_paths.get(font_type, [])}

    if not available_font_types:
        print("错误: 没有任何可用的字体类型")
        return []

    total_prob = sum(available_font_types.values())
    type_samples = {}

    # 计算每种字体类型的样本数（只考虑可用的字体类型）
    for font_type, prob in available_font_types.items():
        count = int(num_samples * (prob / total_prob))
        type_samples[font_type] = count

    # 补足因浮点数计算导致的样本数差额
    remaining = num_samples - sum(type_samples.values())
    if remaining > 0:
        # 将剩余的样本分配给第一个可用的字体类型
        first_available_type = next(iter(available_font_types))
        type_samples[first_available_type] += remaining

    # 抽样并保存为元组 (font_type, font_path)
    sampled_font_pairs = []
    for font_type, count in type_samples.items():
        available_fonts = font_paths.get(font_type, [])
        if not available_fonts:
            print(f"警告: 字体类型 {font_type} 没有可用字体")
            continue

        if count <= 0:
            continue

        # 如果字体数量不足，允许重复抽样
        if count > len(available_fonts):
            sampled = random.choices(available_fonts, k=count)
        else:
            sampled = random.sample(available_fonts, k=count)

        # 处理字体路径并配对
        for path in sampled:
            if not path or not isinstance(path, str):
                print(f"警告: 无效的字体路径: {path}")
                continue
            processed_path = path
            sampled_font_pairs.append((font_type, processed_path))

    if len(sampled_font_pairs) < num_samples:
        print(f"警告: 字体抽样不足，需要 {num_samples} 个，只抽到 {len(sampled_font_pairs)} 个")

    return sampled_font_pairs

def collect_image_files(image_dir):
    """收集图片文件，复用原脚本逻辑"""
    print(f"正在收集图片文件，目录: {image_dir}")

    if not os.path.exists(image_dir):
        print(f"错误: 目录不存在: {image_dir}")
        return []

    if not os.path.isdir(image_dir):
        print(f"错误: 路径不是目录: {image_dir}")
        return []

    image_files = []
    for root, _, files in os.walk(image_dir):
        print(f"正在扫描目录: {root}, 文件数: {len(files)}")
        for file in files:
            if file.lower().endswith(('.jpg', '.png', '.jpeg')):
                full_path = os.path.join(root, file)
                image_files.append(full_path)
                if len(image_files) <= 3:  # 只显示前3个文件用于调试
                    print(f"  找到图片: {full_path}")

    print(f"总共收集到 {len(image_files)} 个图片文件")
    return image_files

def generate_nature_text_colors(num_lines):
    """
    生成自然场景文字颜色：90%白色，10%彩色
    
    参数:
        num_lines: 文字行数
        
    返回:
        text_colors: 颜色列表，每行一个颜色
    """
    text_colors = []
    
    for _ in range(num_lines):
        if random.random() < WHITE_TEXT_PROB:
            # 90%概率生成白色文字
            text_colors.append((255, 255, 255))
        else:
            # 10%概率生成高对比度彩色文字
            h = random.randint(0, 360) / 360.0
            s = random.uniform(0.7, 1.0)  # 高饱和度
            v = random.uniform(0.8, 1.0)  # 高亮度
            rgb = colorsys.hsv_to_rgb(h, s, v)
            text_colors.append(tuple(int(c * 255) for c in rgb))
    
    return text_colors

def calculate_nature_font_sizes(image_size, num_lines):
    """
    基于图像尺寸计算字体大小
    
    参数:
        image_size: 图像尺寸 (width, height)
        num_lines: 文字行数
        
    返回:
        font_sizes: 字体大小列表
    """
    base_size = min(image_size)
    font_sizes = []
    
    for _ in range(num_lines):
        # 在基础字号基础上添加随机变化
        font_size = int(base_size * random.uniform(*FONT_SIZE_RATIO_RANGE))
        # 确保字号在合理范围内
        font_size = max(24, min(font_size, 350))
        font_sizes.append(font_size)
    
    return font_sizes

def choose_mask_strategy():
    """选择Mask策略"""
    rand = random.random()
    cumulative = 0

    for strategy, prob in MASK_STRATEGY_PROB.items():
        cumulative += prob
        if rand <= cumulative:
            return strategy

    # 默认返回部分涂抹
    return 'partial_chars'

def select_nature_erasure_regions(positioned_lines, erasure_mode, expand_boundary=False,
                                 multi_line_erasure=True, left_right_expand_boundary_ratio=0.3):
    """
    为自然场景定制的抹除区域选择函数
    基于原脚本的字符级别选择逻辑，移除彩色块边界限制

    参数:
        positioned_lines: 带位置信息的文本行列表
        erasure_mode: 抹除模式
        expand_boundary: 是否扩展边界
        multi_line_erasure: 是否多行抹除
        left_right_expand_boundary_ratio: 左右扩展边界比例

    返回:
        erasure_regions: 抹除区域列表
    """
    erasure_regions = []

    if not positioned_lines:
        return erasure_regions

    # 筛选有效的文字行
    valid_lines = []
    for line_idx, line in enumerate(positioned_lines):
        if line['chars']:
            valid_lines.append({
                'line_idx': line_idx,
                'line': line,
                'chars': line['chars']
            })

    if not valid_lines:
        return erasure_regions

    # 确定要处理的行数
    if multi_line_erasure:
        num_lines_to_erase = random.randint(1, len(valid_lines))
    else:
        num_lines_to_erase = 1

    # 随机选择要处理的行
    selected_lines = random.sample(valid_lines, num_lines_to_erase)

    # 对每行应用mask策略
    for line_info in selected_lines:
        line_idx = line_info['line_idx']
        line = line_info['line']
        chars = line_info['chars']

        if not chars:
            continue

        # 为每行随机选择抹除模式
        if erasure_mode == 'random':
            line_mode = random.choice(['single_char', 'whole_line', 'partial_line'])
        elif erasure_mode == 'whole_line':
            line_mode = 'whole_line'
        elif erasure_mode == 'partial_chars':
            line_mode = 'partial_line'
        elif erasure_mode == 'single_char':
            line_mode = 'single_char'
        else:
            line_mode = 'single_char'

        # 根据模式选择要抹除的字符
        if line_mode == 'single_char':
            # 随机选择一个字符
            selected_chars = [random.choice(chars)]

        elif line_mode == 'whole_line':
            # 整行字符
            selected_chars = chars

        elif line_mode == 'partial_line':
            # 部分字符：随机选择连续的字符段
            if len(chars) <= 1:
                selected_chars = chars
            else:
                # 随机选择起始和结束位置
                start_idx = random.randint(0, len(chars) - 1)
                end_idx = random.randint(start_idx, len(chars) - 1)
                selected_chars = chars[start_idx:end_idx + 1]
        else:
            selected_chars = chars

        if selected_chars:
            # 基于字符实际渲染位置计算mask区域
            first_char = selected_chars[0]
            last_char = selected_chars[-1]

            # 计算mask区域的位置和尺寸
            region_x = first_char['x']
            region_width = (last_char['x'] + last_char['width']) - first_char['x']

            # 计算字符的实际渲染边界
            line_font = line['font']

            if line_mode == 'single_char':
                # 单字符模式：使用该字符的实际渲染边界
                char_bbox = line_font.getbbox(first_char['char'])
                # 字符实际顶部位置 = 基线位置 + bbox顶部偏移
                actual_top = first_char['y'] + char_bbox[1]
                # 字符实际底部位置 = 基线位置 + bbox底部偏移
                actual_bottom = first_char['y'] + char_bbox[3]

                region_y = actual_bottom  # create_mask_image期望的是底部位置
                region_height = actual_bottom - actual_top  # 实际渲染高度
            else:
                # 整行或部分行模式：计算所有选中字符的边界
                min_top = float('inf')
                max_bottom = float('-inf')

                for char in selected_chars:
                    char_bbox = line_font.getbbox(char['char'])
                    char_top = char['y'] + char_bbox[1]
                    char_bottom = char['y'] + char_bbox[3]
                    min_top = min(min_top, char_top)
                    max_bottom = max(max_bottom, char_bottom)

                region_y = max_bottom  # create_mask_image期望的是底部位置
                region_height = max_bottom - min_top  # 实际渲染高度

            # 创建抹除区域（使用实际渲染边界）
            erasure_regions.append({
                'x': region_x,
                'y': region_y,  # 使用实际字符底部位置
                'width': region_width,
                'height': region_height,  # 使用实际渲染高度
                'chars': selected_chars,
                'line_idx': line_idx,
                'type': line_mode,
                'shape': 'straight'  # 与原脚本一致
            })

    return erasure_regions

def generate_nature_white_text_dataset(output_dir, num_samples, current_idx, font_paths, font_sizes,
                                texture_paths=None, dpi=300, erasure_mode='random', expand_boundary=False,
                                multi_line_erasure=True, left_right_expand_boundary_ratio=0.3,
                                line_spacing=10, char_spacing=5, num_lines=None, chars_per_line=None,
                                use_image_background=False, image_paths=None, text_colors=None,
                                img_name=None, target_size=(1487, 2105)):
    """
    生成自然场景文字抹除数据集

    参数:
        output_dir: 输出目录
        num_samples: 样本数量
        current_idx: 当前索引
        font_paths: 字体路径列表
        font_sizes: 字体大小列表
        texture_paths: 纹理路径（未使用）
        dpi: DPI设置
        erasure_mode: 抹除模式
        expand_boundary: 是否扩展边界
        multi_line_erasure: 是否多行抹除
        left_right_expand_boundary_ratio: 左右扩展边界比例
        line_spacing: 行间距
        char_spacing: 字符间距
        num_lines: 行数
        chars_per_line: 每行字符数
        use_image_background: 是否使用图片背景
        image_paths: 图片路径
        text_colors: 文字颜色列表
        img_name: 图片名称
        target_size: 目标尺寸

    返回:
        bool: 成功返回True，失败返回False
    """
    try:
        # 创建输出目录
        for folder in ['im', 'mask', 'gt', 'show']:
            os.makedirs(os.path.join(output_dir, folder), exist_ok=True)

        # 确保行数与字体列表长度一致
        if num_lines is None:
            num_lines = len(font_paths)
        else:
            num_lines = min(num_lines, len(font_paths))

        # 加载所有字体
        fonts = []
        for i in range(num_lines):
            font_path = font_paths[i][1]
            font_size = font_sizes[i]
            font = load_font_from_tif(font_path, font_size)
            fonts.append([font_paths[i][0], font])

        # 加载背景图片
        if use_image_background and image_paths:
            try:
                # 选择图片
                image_path = random.choice(image_paths) if isinstance(image_paths, list) else image_paths
                if not os.path.exists(image_path):
                    print(f"警告: 图片路径不存在: {image_path}")
                    return False

                # 加载图片
                blank_image = Image.open(image_path)
                # 使用智能缩放
                blank_image = resize_image_with_aspect_ratio(blank_image, target_size)
                image_width, image_height = blank_image.size

                # 如果图片太小，跳过该样本
                if image_width < 100 or image_height < 100:
                    print(f"警告: 图片尺寸太小: {image_path}")
                    return False
            except Exception as e:
                print(f"警告: 加载图片失败: {image_path}, 错误: {e}")
                return False
        else:
            # 创建空白A4图像
            blank_image = create_blank_a4(dpi=dpi)
            blank_image = resize_image_with_aspect_ratio(blank_image, target_size)
            image_width, image_height = blank_image.size

        # 如果不是 RGB 模式，则转换为 RGB
        if blank_image.mode != 'RGB':
            blank_image = blank_image.convert('RGB')

        # 生成文本行
        lines = generate_text_lines(num_lines, chars_per_line, fonts)

        # 计算文本位置（使用图像边界，无彩色块限制）
        positioned_lines = calculate_text_positions(
            lines, image_width, image_height, None,
            margin=50, line_spacing=line_spacing, char_spacing=char_spacing
        )

        if not positioned_lines:
            print("警告: 无法在图像内放置文字")
            return False

        # 在背景图上渲染文字
        original_image = render_text(blank_image.copy(), positioned_lines, None, text_colors=text_colors)
        if original_image is None:
            print("原图渲染文字出错")
            return False

        # 选择抹除区域（使用自然场景专用函数）
        erasure_regions = select_nature_erasure_regions(
            positioned_lines, erasure_mode,
            expand_boundary, multi_line_erasure, left_right_expand_boundary_ratio
        )

        # 创建GT图像（不包含被抹除的文字，显示原始背景）
        gt_image = render_text(blank_image.copy(), positioned_lines, None, erasure_regions, is_gt=True, text_colors=text_colors)
        if gt_image is None:
            print("GT图像渲染文字出错")
            return False

        # 创建掩码图像
        mask_image, _ = create_mask_image((image_width, image_height), erasure_regions, original_image)

        # 创建拼接图像用于可视化
        combined_image = create_combined_image(original_image, gt_image, mask_image, positioned_lines, erasure_regions, None)

        # 保存图像
        if img_name:
            name = os.path.splitext(os.path.basename(image_path))[0]
            original_path = os.path.join(output_dir, 'im', name + "_" + str(current_idx) + ".png")
            mask_path = os.path.join(output_dir, 'mask', name + "_" + str(current_idx) + ".png")
            gt_path = os.path.join(output_dir, 'gt', name + "_" + str(current_idx) + ".png")
            show_path = os.path.join(output_dir, 'show', name + "_" + str(current_idx) + ".png")
        else:
            original_path = os.path.join(output_dir, 'im', f'sample_{current_idx:05d}.png')
            mask_path = os.path.join(output_dir, 'mask', f'sample_{current_idx:05d}.png')
            gt_path = os.path.join(output_dir, 'gt', f'sample_{current_idx:05d}.png')
            show_path = os.path.join(output_dir, 'show', f'sample_{current_idx:05d}.png')

        if os.path.exists(original_path):
            print(f"{original_path} 已存在，跳过")
            return False

        original_image.save(original_path)
        mask_image.save(mask_path)
        gt_image.save(gt_path)
        combined_image.save(show_path)

        return True

    except Exception as e:
        print(f"生成自然场景文字数据集时出错 (current_idx: {current_idx}): {e}")
        return False

def generate_samples_task(args):
    """生成样本的任务函数，基于原脚本结构修改"""
    output_folder, shared, samples_to_generate, process_id = args
    if not shared:
        raise ValueError("Shared list is empty in process %d" % process_id)

    # 检查共享状态
    if not hasattr(shared, 'image_files') or not shared.image_files:
        raise ValueError(f"[进程 {process_id}] 共享图片列表为空")

    if not hasattr(shared, 'total_images') or shared.total_images <= 0:
        raise ValueError(f"[进程 {process_id}] 图片总数无效: {getattr(shared, 'total_images', 'None')}")

    print(f"[进程 {process_id}] 开始生成，图片总数: {shared.total_images}")

    # 收集字体文件
    font_paths = collect_font_paths()

    # 检查字体可用性
    total_fonts = sum(len(fonts) for fonts in font_paths.values())
    if total_fonts == 0:
        print(f"[进程 {process_id}] 错误: 没有找到任何可用字体")
        return

    print(f"[进程 {process_id}] 可用字体统计:")
    for font_type, fonts in font_paths.items():
        print(f"  {font_type}: {len(fonts)} 个字体")

    # 如果字体总数太少，调整生成策略
    if total_fonts < 10:
        print(f"[进程 {process_id}] 警告: 字体数量较少({total_fonts}个)，可能影响生成质量")

    generated = 0
    failed_attempts = 0
    max_failed_attempts = samples_to_generate * 2  # 允许失败次数为目标样本数的2倍

    while generated < samples_to_generate and failed_attempts < max_failed_attempts:
        try:
            # 选择图片
            with shared.lock:
                # 再次检查以防竞态条件
                if not shared.image_files or shared.total_images <= 0:
                    raise ValueError(f"[进程 {process_id}] 共享状态异常: 图片列表长度={len(shared.image_files) if shared.image_files else 0}, 总数={shared.total_images}")

                current_index = shared.image_index
                shared.image_index += 1
                image_path = shared.image_files[current_index % shared.total_images]

            # 检查图片文件是否存在和有效
            if not os.path.exists(image_path):
                print(f"[进程 {process_id}] 图片文件不存在: {image_path}")
                failed_attempts += 1
                continue

            try:
                # 加载背景图片获取尺寸
                bg_image = Image.open(image_path)
                image_size = bg_image.size
                bg_image.close()  # 及时关闭图片
            except Exception as e:
                print(f"[进程 {process_id}] 无法加载图片 {image_path}: {e}")
                failed_attempts += 1
                continue

            # 生成文字配置
            num_lines = random.randint(*NUM_LINES_RANGE)
            chars_per_line = random.randint(*CHARS_PER_LINE_RANGE)

            print(f"文字配置: {num_lines}行 x {chars_per_line}字符")

            # 随机抽样字体
            fonts_type = sample_fonts(font_paths, num_lines)

            # 检查字体抽样结果
            if not fonts_type:
                print(f"[进程 {process_id}] 警告: 字体抽样失败，跳过该样本")
                failed_attempts += 1
                continue

            if len(fonts_type) < num_lines:
                print(f"[进程 {process_id}] 警告: 字体数量不足，需要{num_lines}个，只有{len(fonts_type)}个，跳过该样本")
                failed_attempts += 1
                continue

            # 基于图像尺寸计算字体大小
            font_sizes = calculate_nature_font_sizes(image_size, num_lines)

            print(f"确定字体大小: {font_sizes} (基于图像短边: {min(image_size)}, 比例: {FONT_SIZE_RATIO_RANGE})")

            # 生成文字颜色（90%白色，10%彩色）
            text_colors = generate_nature_text_colors(num_lines)

            line_spacing = random.randint(*LINE_SPACING_RANGE)
            char_spacing = random.randint(*CHAR_SPACING_RANGE)

            # 选择Mask策略
            mask_strategy = choose_mask_strategy()

            print(f"文字配置: {num_lines}行 x {chars_per_line}字符, 平均字号: {sum(font_sizes)/len(font_sizes):.1f}")
            print(f"文字颜色: {len([c for c in text_colors if c == (255, 255, 255)])} 白色, {len([c for c in text_colors if c != (255, 255, 255)])} 彩色")

            # 调用生成函数
            success = generate_nature_white_text_dataset(
                output_folder,
                1,
                current_index,
                fonts_type,
                font_sizes,
                None,
                dpi=300,
                erasure_mode=mask_strategy,
                expand_boundary=True,
                multi_line_erasure=True,
                left_right_expand_boundary_ratio=0.3,
                line_spacing=line_spacing,
                char_spacing=char_spacing,
                num_lines=num_lines,
                chars_per_line=chars_per_line,
                use_image_background=True,
                image_paths=image_path,
                text_colors=text_colors,
                img_name=os.path.basename(image_path),
                target_size=image_size  # 使用实际图像尺寸
            )

            if success:
                generated += 1
                # 更新共享计数器
                with shared.lock:
                    shared.actual_generated += 1
                # 进度显示
                if generated % 10 == 0:
                    print(f"[进程 {process_id}] 已生成 {generated}/{samples_to_generate} 个样本")
            else:
                failed_attempts += 1

        except Exception as e:
            print(f"[进程 {process_id}] 生成样本时出错: {e}")
            failed_attempts += 1
            continue

    if failed_attempts >= max_failed_attempts:
        print(f"[进程 {process_id}] 警告：失败次数过多，提前结束。已生成 {generated}/{samples_to_generate} 个样本")

def generate_samples_parallel(image_folder, output_folder, total_samples=10000, num_processes=4):
    """并行生成样本，复用原脚本结构"""
    print(f"开始处理文件夹: {image_folder}")

    # 创建输出目录
    for folder in ['im', 'mask', 'gt', 'show']:
        os.makedirs(os.path.join(output_folder, folder), exist_ok=True)

    # 收集图片文件
    image_files = collect_image_files(image_folder)
    print(f"收集到 {len(image_files)} 个图片文件")

    if not image_files:
        print(f"警告：{image_folder} 中没有图片文件")
        return

    # 显示前几个图片文件路径用于调试
    print("前5个图片文件:")
    for i, img_path in enumerate(image_files[:5]):
        print(f"  {i+1}: {img_path}")

    # 初始化共享状态
    shared = init_shared_state(image_files)
    print(f"共享状态初始化完成，图片总数: {shared.total_images}")
    # 计算每个进程应生成的样本数
    samples_per_process = max(1, total_samples // num_processes)

    # 创建进程池
    with Pool(processes=num_processes) as pool:
        args_list = [(output_folder, shared, samples_per_process, i) for i in range(num_processes)]
        pool.map(generate_samples_task, args_list)
        # 等待所有进程完成
        pool.close()
        pool.join()

    # 输出统计信息
    print(f"{image_folder} 处理完成")
    print(f"生成统计：")
    print(f"- 目标样本数: {total_samples}")
    print(f"- 实际生成数: {shared.actual_generated}")
    print(f"- 成功率: {shared.actual_generated/total_samples*100:.1f}%")
    print(f"图片使用统计：")
    print(f"- 顺序使用图片数量: {min(shared.image_index, shared.total_images)}")
    print(f"- 随机重复使用次数: {max(0, shared.image_index - shared.total_images)}")

def check_paths():
    """检查必要的路径是否存在"""
    # 检查字体路径
    if not os.path.exists(FONT_PATH):
        print(f"❌ 错误：字体文件夹 {FONT_PATH} 不存在")
        print("请确保字体文件夹存在，或修改 FONT_PATH 配置")
        return False

    # 检查图片路径
    if not os.path.exists(IMAGE_PATH):
        print(f"❌ 错误：背景图片文件夹 {IMAGE_PATH} 不存在")
        print("请确保背景图片文件夹存在，或修改 IMAGE_PATH 配置")
        return False

    # 创建输出目录
    try:
        os.makedirs(OUTPUT_PATH, exist_ok=True)
        # 创建输出子目录
        for folder in ['im', 'mask', 'gt', 'show']:
            os.makedirs(os.path.join(OUTPUT_PATH, folder), exist_ok=True)
        print(f"✅ 输出目录已创建：{OUTPUT_PATH}")
    except Exception as e:
        print(f"❌ 错误：无法创建输出目录 {OUTPUT_PATH}: {e}")
        return False

    return True

def main():
    """主函数，复用原脚本结构"""
    print("=" * 60)
    print("自然场景文字消除数据集生成脚本")
    print("=" * 60)

    # 检查路径配置
    if not check_paths():
        print("请检查路径配置后重新运行脚本")
        return

    # 检查图片子文件夹
    image_subfolders = []
    if os.path.isdir(IMAGE_PATH):
        # 如果IMAGE_PATH直接包含图片文件，直接使用
        image_files = [f for f in os.listdir(IMAGE_PATH)
                      if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
        if image_files:
            image_subfolders = ['.']  # 使用当前目录
        else:
            # 查找子文件夹
            for item in os.listdir(IMAGE_PATH):
                item_path = os.path.join(IMAGE_PATH, item)
                if os.path.isdir(item_path):
                    # 检查子文件夹是否包含图片
                    sub_images = [f for f in os.listdir(item_path)
                                 if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
                    if sub_images:
                        image_subfolders.append(item)

    if not image_subfolders:
        print(f"❌ 错误：在 {IMAGE_PATH} 中未找到任何图片文件")
        return

    print(f"发现图片文件夹：{image_subfolders}")

    # 遍历图片文件夹
    for image_subfolder in image_subfolders:
        if image_subfolder == '.':
            input_path = IMAGE_PATH
            print(f"开始处理文件夹：{input_path}")
        else:
            input_path = os.path.join(IMAGE_PATH, image_subfolder)
            print(f"开始处理文件夹：{input_path}")

        generate_samples_parallel(
            image_folder=input_path,
            output_folder=OUTPUT_PATH,
            total_samples=SAMPLES_PER_FOLDER,
            num_processes=6
        )

    print("=" * 60)
    print("数据集生成完成！")
    print(f"输出目录：{OUTPUT_PATH}")
    print("=" * 60)

if __name__ == "__main__":
    main()
