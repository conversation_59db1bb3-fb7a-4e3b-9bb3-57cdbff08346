# 数据集制作指导文档

## 概述

本文档指导如何使用现有脚本制作用于文字消除（inpainting）任务的训练数据集。数据集制作分为四个主要阶段：纯净数据制作、降质数据制作、扭曲数据制作和训练数据JSONL生成。

## 数据集制作流程

### 阶段一：纯净数据制作

纯净数据是指高质量、无噪声的原始数据，用作训练的基础。

#### 1. 表格数据制作

**脚本：** `Genetate_tableDataset.py`

**功能：** 生成正常表格数据，支持表格中存在颜色，模拟各种字体的消除

**主要特性：**
- 支持多种背景颜色（90%白色，5%浅灰，5%深灰）
- 随机生成表格行列数（10-20列）
- 支持单元格合并
- 支持行/列高亮显示
- 多进程并行生成

**使用方法：**
```bash
conda activate py39
python Genetate_tableDataset.py
```

**输出目录结构：**
```
output_dir/
├── im/     # 原始图像
├── gt/     # 处理后的图像（去除mask区域文字）
├── mask/   # 掩码图像
└── show/   # 可视化图像
```

#### 2. 表格跨表格线消除

**脚本：** `Genetate_tableDataset_cross_cell.py`（需要确认是否存在）

**目的：** 确保表格线不被消除，专门处理跨单元格的文字消除场景

#### 3. 中英文文档数据

**脚本：** `batch_generate_zh_en_doc.py`

**功能：** 批量生成中英文PDF，模拟文档类型数据

**主要参数：**
- `total_images`: 生成图像总数
- `num_threads`: 线程数
- `char_n_scope`: 字符数量范围
- `mask_shape_type_list`: 掩码形状类型（'rect', 'straight', 'aa'）

**使用方法：**
```bash
conda activate py39
python batch_generate_zh_en_doc.py
```

#### 4. 自然场景文字消除

**脚本：** `nature_generate_script.py`

**功能：** 在自然场景图像上添加文字并生成消除数据

**主要配置：**
- `FONT_PATH`: 字体文件夹路径
- `IMAGE_PATH`: 背景图片文件夹路径
- `OUTPUT_PATH`: 输出文件夹路径
- `SAMPLES_PER_FOLDER`: 每个文件夹生成样本数

**字体类型概率：**
- 简体中文：40%
- 数字：30%
- 英文字母：20%
- 繁体中文：10%

**使用方法：**
```bash
conda activate py39
python nature_generate_script.py
```

### 阶段二：降质数据制作

**脚本：** `xiaochu_degrade_enhance.py`

**功能：** 将纯净数据进行降质处理，模拟真实场景中的图像质量问题

**主要降质类型：**
- 模糊处理
- 噪声添加
- 压缩伪影
- 光照变化
- 几何变形

**使用方法：**
```bash
conda activate py39
python xiaochu_degrade_enhance.py
```

**输入要求：**
- 需要包含 `mask/`, `im/`, `gt/` 三个子文件夹的数据集
- 同名图像文件

### 阶段三：扭曲数据制作

**脚本位置：** 其他仓库
```
https://gitlab.liebaopay.com/huzhi/uvdoc-dataset/-/blob/myself/hz_enahnce_inpainting_uvdoc.py
```

**功能：** 对数据进行几何扭曲处理，增强模型对变形文档的处理能力

### 阶段四：训练数据JSONL生成

**脚本：** `create_dataset_crop_jsonl_mp.py`

**功能：** 将制作好的数据集转换为训练所需的JSONL格式

**主要功能：**
- 基于掩码轮廓的智能裁剪
- 多进程并行处理
- 支持数据增强
- 生成训练/验证集分割

**使用方法：**
```bash
conda activate py39
python create_dataset_crop_jsonl_mp.py
```

## 完整制作流程

### 步骤1：环境准备
```bash
conda activate py39
cd examples/inpainting/
```

### 步骤2：制作纯净数据
根据需要选择以下一种或多种数据类型：

**表格数据：**
```bash
python Genetate_tableDataset.py
```

**文档数据：**
```bash
python batch_generate_zh_en_doc.py
```

**自然场景数据：**
```bash
python nature_generate_script.py
```

**自然场景+彩色块数据：**
```bash
python nature_colorblock_generate_script.py
```

### 步骤3：数据降质
```bash
python xiaochu_degrade_enhance.py
```

### 步骤4：数据扭曲（可选）
使用外部仓库脚本进行几何扭曲处理

### 步骤5：生成训练JSONL
```bash
python create_dataset_crop_jsonl_mp.py
```

## 数据集验证

使用以下脚本验证生成的数据集：

**数据集检查：**
```bash
python check_dataset.py
python Verify_dataset.py
```

**可视化展示：**
```bash
python visual_show.py
```

## 注意事项

1. **路径配置**：使用前需要根据实际情况修改各脚本中的路径配置
2. **资源要求**：大规模数据生成需要充足的存储空间和计算资源
3. **多进程设置**：根据机器性能调整并行进程数
4. **数据质量**：定期检查生成数据的质量，确保符合训练要求
5. **版本管理**：建议对不同版本的数据集进行版本管理

#### 5. 自然场景+彩色块数据

**脚本：** `nature_colorblock_generate_script.py`

**功能：** 在自然场景图像上生成彩色块并添加文字，专门用于训练彩色块上的文字消除

**主要特性：**
- 每张图片生成1个彩色块（60%圆角矩形，30%椭圆，10%不规则多边形）
- 彩色块面积占图片总面积的5%-30%
- 90%白色文字，10%彩色文字（与背景形成高对比度）
- 文字严格限制在彩色块内部，保持5%安全边距
- Mask只覆盖文字区域，不超出彩色块边界

**使用方法：**
```bash
conda activate py39
python nature_colorblock_generate_script.py
```

## 辅助工具

- `xiaochu_tools.py`: 包含数据处理的通用工具函数
- `split_train_val_collect.py`: 训练验证集分割工具
- `random_copy.py`: 随机复制数据工具
- `detele_folder.py`: 文件夹清理工具

## 故障排除

1. **内存不足**：减少并行进程数或批处理大小
2. **路径错误**：检查所有路径配置是否正确
3. **字体缺失**：确保字体文件夹包含所需字体
4. **权限问题**：确保对输出目录有写权限

通过以上流程，您可以制作出高质量的文字消除训练数据集。建议从小规模测试开始，确认流程无误后再进行大规模生成。
