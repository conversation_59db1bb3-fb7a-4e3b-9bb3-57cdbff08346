#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集文件夹清理脚本
检查并清理 im, mask, gt, show 文件夹中不匹配的文件

使用方法:
1. 检查并清理不匹配的文件:
   python fix.py <数据集文件夹路径>

2. 自动删除不匹配的文件（不询问确认）:
   python fix.py <数据集文件夹路径> --auto-delete

3. 删除最新的N个文件组:
   python fix.py <数据集文件夹路径> --delete-newest N

4. 随机删除N个文件组:
   python fix.py <数据集文件夹路径> --delete-random N

例如:
python fix.py /path/to/dataset
python fix.py /path/to/dataset --auto-delete
python fix.py /path/to/dataset --delete-newest 10
python fix.py /path/to/dataset --delete-random 5
"""

import os
import sys
from pathlib import Path
from collections import defaultdict
import argparse
import random

def get_file_stems(folder_path):
    """
    获取文件夹中所有文件的文件名（不含扩展名）
    
    参数:
        folder_path: 文件夹路径
    
    返回:
        set: 文件名集合（不含扩展名）
    """
    if not os.path.exists(folder_path):
        return set()
    
    stems = set()
    for file in os.listdir(folder_path):
        if os.path.isfile(os.path.join(folder_path, file)):
            # 获取文件名（不含扩展名）
            stem = Path(file).stem
            stems.add(stem)
    
    return stems

def get_files_by_mtime(folder_path):
    """
    按修改时间排序获取文件夹中的文件
    
    参数:
        folder_path: 文件夹路径
    
    返回:
        list: 按修改时间排序的文件列表（最新的在前）
    """
    if not os.path.exists(folder_path):
        return []
    
    files = []
    for file in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file)
        if os.path.isfile(file_path):
            mtime = os.path.getmtime(file_path)
            files.append((file, file_path, mtime))
    
    # 按修改时间降序排序（最新的在前）
    files.sort(key=lambda x: x[2], reverse=True)
    
    return files

def find_mismatched_files(dataset_path):
    """
    查找不匹配的文件
    
    参数:
        dataset_path: 数据集根目录路径
    
    返回:
        dict: 包含不匹配文件信息的字典
    """
    folders = ['im', 'mask', 'gt', 'show']
    folder_paths = {}
    file_stems = {}
    
    # 检查文件夹是否存在
    for folder in folders:
        folder_path = os.path.join(dataset_path, folder)
        if not os.path.exists(folder_path):
            print(f"警告: 文件夹 {folder_path} 不存在")
            return None
        folder_paths[folder] = folder_path
        file_stems[folder] = get_file_stems(folder_path)
    
    # 找到所有文件夹的交集（应该存在于所有文件夹的文件）
    all_stems = set()
    for stems in file_stems.values():
        all_stems.update(stems)
    
    # 找到完整的文件集合（在所有4个文件夹中都存在的文件）
    complete_stems = file_stems['im']
    for folder in folders[1:]:
        complete_stems = complete_stems.intersection(file_stems[folder])
    
    # 找到每个文件夹中多余的文件
    mismatched_files = {}
    total_mismatched = 0
    
    for folder in folders:
        extra_files = file_stems[folder] - complete_stems
        if extra_files:
            # 获取这些文件的完整路径和修改时间
            folder_files = get_files_by_mtime(folder_paths[folder])
            extra_file_info = []
            
            for file_name, file_path, mtime in folder_files:
                stem = Path(file_name).stem
                if stem in extra_files:
                    extra_file_info.append({
                        'name': file_name,
                        'path': file_path,
                        'stem': stem,
                        'mtime': mtime
                    })
            
            # 按修改时间排序（最新的在前）
            extra_file_info.sort(key=lambda x: x['mtime'], reverse=True)
            
            mismatched_files[folder] = extra_file_info
            total_mismatched += len(extra_file_info)
    
    return {
        'mismatched_files': mismatched_files,
        'total_mismatched': total_mismatched,
        'complete_stems': complete_stems,
        'folder_paths': folder_paths
    }

def print_mismatched_files(result):
    """
    打印不匹配的文件信息
    
    参数:
        result: find_mismatched_files 的返回结果
    """
    if not result:
        return
    
    mismatched_files = result['mismatched_files']
    total_mismatched = result['total_mismatched']
    complete_stems = result['complete_stems']
    
    print("=" * 60)
    print("数据集文件检查结果")
    print("=" * 60)
    print(f"完整的文件数量: {len(complete_stems)}")
    print(f"不匹配的文件总数: {total_mismatched}")
    print()
    
    if total_mismatched == 0:
        print("✅ 所有文件都匹配，无需清理！")
        return
    
    print("不匹配的文件详情（按最新时间排序）:")
    print("-" * 60)
    
    for folder, files in mismatched_files.items():
        if files:
            print(f"\n📁 {folder} 文件夹中多余的文件 ({len(files)} 个):")
            for i, file_info in enumerate(files, 1):
                import time
                mtime_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_info['mtime']))
                print(f"  {i:2d}. {file_info['name']} (修改时间: {mtime_str})")

def delete_mismatched_files(result):
    """
    删除不匹配的文件

    参数:
        result: find_mismatched_files 的返回结果
    """
    if not result:
        return

    mismatched_files = result['mismatched_files']
    deleted_count = 0

    for folder, files in mismatched_files.items():
        if files:
            print(f"\n正在删除 {folder} 文件夹中的多余文件...")
            for file_info in files:
                try:
                    os.remove(file_info['path'])
                    print(f"  ✅ 已删除: {file_info['name']}")
                    deleted_count += 1
                except Exception as e:
                    print(f"  ❌ 删除失败: {file_info['name']} - {e}")

    print(f"\n删除完成，共删除 {deleted_count} 个文件")

def get_newest_files(dataset_path, count):
    """
    获取数据集中最新的x个文件（基于修改时间）

    参数:
        dataset_path: 数据集根目录路径
        count: 要获取的文件数量

    返回:
        list: 最新的文件信息列表
    """
    folders = ['im', 'mask', 'gt', 'show']
    all_files = []

    # 收集所有文件夹中的文件
    for folder in folders:
        folder_path = os.path.join(dataset_path, folder)
        if os.path.exists(folder_path):
            files = get_files_by_mtime(folder_path)
            for file_name, file_path, mtime in files:
                stem = Path(file_name).stem
                all_files.append({
                    'stem': stem,
                    'folder': folder,
                    'name': file_name,
                    'path': file_path,
                    'mtime': mtime
                })

    # 按修改时间排序（最新的在前）
    all_files.sort(key=lambda x: x['mtime'], reverse=True)

    # 按文件stem分组，找到完整的文件组（在所有4个文件夹中都存在）
    file_groups = defaultdict(list)
    for file_info in all_files:
        file_groups[file_info['stem']].append(file_info)

    # 只保留完整的文件组（在所有4个文件夹中都存在）
    complete_groups = []
    for stem, files in file_groups.items():
        if len(files) == 4:  # 在所有4个文件夹中都存在
            # 使用该组中最新文件的时间作为组的时间
            group_mtime = max(f['mtime'] for f in files)
            complete_groups.append({
                'stem': stem,
                'files': files,
                'mtime': group_mtime
            })

    # 按组的时间排序（最新的在前）
    complete_groups.sort(key=lambda x: x['mtime'], reverse=True)

    # 返回最新的count个文件组
    return complete_groups[:count]

def delete_newest_files(dataset_path, count):
    """
    删除数据集中最新的x个文件

    参数:
        dataset_path: 数据集根目录路径
        count: 要删除的文件数量
    """
    newest_files = get_newest_files(dataset_path, count)

    if not newest_files:
        print("没有找到完整的文件组可以删除")
        return

    print(f"\n找到 {len(newest_files)} 个最新的完整文件组:")
    print("-" * 60)

    for i, group in enumerate(newest_files, 1):
        import time
        mtime_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(group['mtime']))
        print(f"{i:2d}. {group['stem']} (最新修改时间: {mtime_str})")
        for file_info in group['files']:
            print(f"    - {file_info['folder']}/{file_info['name']}")

    print("\n" + "=" * 60)
    response = input(f"确认删除这 {len(newest_files)} 个文件组（共 {len(newest_files) * 4} 个文件）？(y/N): ").strip().lower()

    if response in ['y', 'yes', '是']:
        deleted_count = 0
        for group in newest_files:
            print(f"\n正在删除文件组: {group['stem']}")
            for file_info in group['files']:
                try:
                    os.remove(file_info['path'])
                    print(f"  ✅ 已删除: {file_info['folder']}/{file_info['name']}")
                    deleted_count += 1
                except Exception as e:
                    print(f"  ❌ 删除失败: {file_info['folder']}/{file_info['name']} - {e}")

        print(f"\n删除完成，共删除 {deleted_count} 个文件")
    else:
        print("已取消删除操作")

def get_random_files(dataset_path, count):
    """
    随机获取数据集中的x个文件组

    参数:
        dataset_path: 数据集根目录路径
        count: 要获取的文件数量

    返回:
        list: 随机选择的文件信息列表
    """
    folders = ['im', 'mask', 'gt', 'show']
    all_files = []

    # 收集所有文件夹中的文件
    for folder in folders:
        folder_path = os.path.join(dataset_path, folder)
        if os.path.exists(folder_path):
            files = get_files_by_mtime(folder_path)
            for file_name, file_path, mtime in files:
                stem = Path(file_name).stem
                all_files.append({
                    'stem': stem,
                    'folder': folder,
                    'name': file_name,
                    'path': file_path,
                    'mtime': mtime
                })

    # 按文件stem分组，找到完整的文件组（在所有4个文件夹中都存在）
    file_groups = defaultdict(list)
    for file_info in all_files:
        file_groups[file_info['stem']].append(file_info)

    # 只保留完整的文件组（在所有4个文件夹中都存在）
    complete_groups = []
    for stem, files in file_groups.items():
        if len(files) == 4:  # 在所有4个文件夹中都存在
            # 使用该组中最新文件的时间作为组的时间
            group_mtime = max(f['mtime'] for f in files)
            complete_groups.append({
                'stem': stem,
                'files': files,
                'mtime': group_mtime
            })

    # 随机选择count个文件组
    if len(complete_groups) <= count:
        return complete_groups
    else:
        return random.sample(complete_groups, count)

def delete_random_files(dataset_path, count):
    """
    随机删除数据集中的x个文件组

    参数:
        dataset_path: 数据集根目录路径
        count: 要删除的文件数量
    """
    random_files = get_random_files(dataset_path, count)

    if not random_files:
        print("没有找到完整的文件组可以删除")
        return

    print(f"\n随机选择了 {len(random_files)} 个完整文件组:")
    print("-" * 60)

    for i, group in enumerate(random_files, 1):
        import time
        mtime_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(group['mtime']))
        print(f"{i:2d}. {group['stem']} (最新修改时间: {mtime_str})")
        for file_info in group['files']:
            print(f"    - {file_info['folder']}/{file_info['name']}")

    print("\n" + "=" * 60)
    response = input(f"确认删除这 {len(random_files)} 个文件组（共 {len(random_files) * 4} 个文件）？(y/N): ").strip().lower()

    if response in ['y', 'yes', '是']:
        deleted_count = 0
        for group in random_files:
            print(f"\n正在删除文件组: {group['stem']}")
            for file_info in group['files']:
                try:
                    os.remove(file_info['path'])
                    print(f"  ✅ 已删除: {file_info['folder']}/{file_info['name']}")
                    deleted_count += 1
                except Exception as e:
                    print(f"  ❌ 删除失败: {file_info['folder']}/{file_info['name']} - {e}")

        print(f"\n删除完成，共删除 {deleted_count} 个文件")
    else:
        print("已取消删除操作")

def main():
    parser = argparse.ArgumentParser(description='数据集文件夹清理脚本')
    parser.add_argument('dataset_path', help='数据集文件夹路径')
    parser.add_argument('--auto-delete', action='store_true', help='自动删除不匹配的文件，不询问确认')
    parser.add_argument('--delete-newest', type=int, metavar='N', help='删除最新的N个文件组')
    parser.add_argument('--delete-random', type=int, metavar='N', help='随机删除N个文件组')

    args = parser.parse_args()

    dataset_path = args.dataset_path

    # 检查数据集路径是否存在
    if not os.path.exists(dataset_path):
        print(f"❌ 错误: 数据集路径不存在: {dataset_path}")
        sys.exit(1)

    if not os.path.isdir(dataset_path):
        print(f"❌ 错误: 路径不是文件夹: {dataset_path}")
        sys.exit(1)

    print(f"正在检查数据集: {dataset_path}")

    # 如果指定了删除最新文件的选项
    if args.delete_newest:
        delete_newest_files(dataset_path, args.delete_newest)
        print("\n脚本执行完成！")
        return

    # 如果指定了随机删除文件的选项
    if args.delete_random:
        delete_random_files(dataset_path, args.delete_random)
        print("\n脚本执行完成！")
        return

    # 查找不匹配的文件
    result = find_mismatched_files(dataset_path)

    if not result:
        print("❌ 检查失败，请确保数据集文件夹包含 im, mask, gt, show 子文件夹")
        sys.exit(1)

    # 打印结果
    print_mismatched_files(result)

    # 如果有不匹配的文件，询问是否删除
    if result['total_mismatched'] > 0:
        if args.auto_delete:
            print("\n自动删除模式，开始删除不匹配的文件...")
            delete_mismatched_files(result)
        else:
            print("\n" + "=" * 60)
            response = input(f"是否删除这 {result['total_mismatched']} 个不匹配的文件？(y/N): ").strip().lower()

            if response in ['y', 'yes', '是']:
                delete_mismatched_files(result)
            else:
                print("已取消删除操作")

    print("\n脚本执行完成！")

if __name__ == "__main__":
    main()
