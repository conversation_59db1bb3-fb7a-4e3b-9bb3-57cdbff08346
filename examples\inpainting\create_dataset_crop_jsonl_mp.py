#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/12/2 14:14
# <AUTHOR> <EMAIL>
# @FileName: create_dataset_jsonl
import glob
import os
import random
import json
from pathlib import Path
from tqdm import tqdm  # 引入 tqdm
from modules.utils.image_utils import get_all_image_path_v2
from PIL import Image
import multiprocessing as mp
# 数据集根目录
# dataset_root = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/alldata_json/"  #存放json 原本位置

# dataset_root = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/table_cross_cell_json/"  #存放新数据位置
# dataset_root = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/watermark_removal_dataset_json/"  #存放水印数据 json
dataset_root = r"/aicamera-mlp/hz_datasets/xiaochu_dataset/oyy_white_train"  #存放新数据位置 - 修改为直接输出到数据集根目录
os.makedirs(dataset_root, exist_ok=True)
root_path = "/aicamera-mlp/hz_datasets/xiaochu_dataset/" #数据集
def crop_by_mask_contours(image_list, mask_index=1, expand_ratio=0.1, max_crops=5,mode='fix', target_size=None, sampling=None):
    """
    基于掩码轮廓的智能裁剪函数

    Args:
        image_list: 图像列表，包含原图和掩码图像
        mask_index: 掩码图像在image_list中的索引，默认为1
        expand_ratio: 正方形外扩比例，范围0-1，默认为0.1 左右俩边总共比原来多加0.3 设置一下 左右比原来多加0.6
        max_crops: 从一个掩码最多裁剪的轮廓数量，默认为5
        mode: adaptive fix
        target_size: 裁剪后调整到的目标尺寸，默认为None（不调整）
        sampling: 重采样方法，默认为None

    Returns:
        裁剪后的图像列表，每个元素包含裁剪后的原图和对应掩码
    """
    import numpy as np
    import cv2

    # 确保输入有效
    if len(image_list) <= mask_index:
        raise ValueError(f"mask_index {mask_index} 超出了image_list的范围")

    # 获取掩码图像并转换为numpy数组
    mask_pil = image_list[mask_index]
    mask_np = np.array(mask_pil)

    # 确保掩码是二值图像
    if mask_np.ndim > 2:
        # 如果是RGB或RGBA，转换为灰度
        mask_np = cv2.cvtColor(mask_np, cv2.COLOR_RGB2GRAY)

    # 二值化处理
    _, binary_mask = cv2.threshold(mask_np, 1, 255, cv2.THRESH_BINARY)

    # 查找轮廓
    contours,_ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    contours=list(contours)
    # 刷新一下轮廓
    random.shuffle(contours)

    # 获取图像尺寸
    img_width, img_height = image_list[0].size

    # 存储裁剪结果
    cropped_results = []
    crop_index_list=[]
    max_crops=min(len(contours),max_crops) # 防止mask太少
    box_areas = []
    # 处理每个轮廓，最多处理max_crops个
    for contour in contours[:max_crops]:
        # 获取最小外接矩形
        x, y, w, h = cv2.boundingRect(contour)
        #跳过外接矩面积小于20的轮廓
        if(w*h<20):
            max_crops = min(len(contours),max_crops+1) #保持数量不变，max_crops尽量+1
            continue
        # 计算矩形中心点
        center_x = x + w // 2
        center_y = y + h // 2
        max_side=None
        if mode=='fix':# 固定512尺寸
            max_side=512
        elif mode=='adaptive': # 自适应模式
            # 获取最长边
            max_side = max(w, h)
        # 应用外扩比例
        expanded_side = int(max_side * (1 + expand_ratio))
        if mode == 'rect':
            # 矩形框的最小外接矩
            left = x
            top = y
            right = x + w
            bottom = y + h
            # 外扩距离
            expand_n = max(w, h) * expand_ratio
            left = left-expand_n if (left-expand_n) >=0 else 0
            top = top-expand_n if (top-expand_n) >=0 else 0
            right = right+expand_n if ( right+expand_n) < img_width else img_width
            bottom = bottom + expand_n if (bottom + expand_n) < img_height else img_height
        else:
            # 计算矩形中心点
            center_x = x + w // 2
            center_y = y + h // 2
            max_side=None
            if mode=='fix':# 固定512尺寸
                max_side=512
            elif mode=='adaptive': # 自适应模式
                # 获取最长边
                max_side = max(w, h)

            # 应用外扩比例
            expanded_side = int(max_side * (1 + expand_ratio))

        # 计算正方形的左上角和右下角坐标
        half_side = expanded_side // 2
        left = center_x - half_side
        top = center_y - half_side
        right = center_x + half_side
        bottom = center_y + half_side

        # 检查是否超出图像边界
        if left < 0 or top < 0 or right > img_width or bottom > img_height:
            if mode == 'adaptive':
                # 保持中心点不变，缩小正方形大小
                
                # 计算中心点到各边界的距离
                dist_to_left = center_x
                dist_to_right = img_width - center_x
                dist_to_top = center_y
                dist_to_bottom = img_height - center_y
                
                # 找出最小距离，这将决定正方形的最大可能边长
                min_dist = min(dist_to_left, dist_to_right, dist_to_top, dist_to_bottom)
                
                # 新的半边长（确保是正方形）
                new_half_side = min_dist
                
                # 重新计算裁剪区域坐标
                left = center_x - new_half_side
                top = center_y - new_half_side
                right = center_x + new_half_side
                bottom = center_y + new_half_side
                
                # 确保坐标在图像边界内（理论上不需要，但为了安全）
                left = max(0, left)
                top = max(0, top)
                right = min(img_width, right)
                bottom = min(img_height, bottom)
            else:
                # 对于非adaptive模式，保持原来的逻辑，超出边界则跳过
                continue

        # 裁剪区域
        crop_box = (left, top, right, bottom)# xmin,ymin,xmax,ymax
        # 这个裁切区域可以保存在dataset里面进行裁切
        crop_index_list.append(crop_box)

    return crop_index_list

# 数据集配置
## Release
product_mode=True # 是否是生产环境
print(f'是否是生产环境:{product_mode}')
if product_mode: # 生成环境数据集
    dataset_names = [
        # (数据名，随机采样数量，-1则默认全部)
        # 原有数据集配置 - 保留作为参考
        # ("doc_ch_en1_filter", 1000),  # pdf 纯净版
        # ("doc_ch_en1_filter2", -1),  # pdf 纯净版
        # ("user",-1) #用户数据
        # ("doc_ch_en1_filter_enhance", -1),  # pdf 降质版 -1
        # ("doc_ch_en1_filter_uvdoc", -1),  # pdf 扭曲版-1
        # ("doc_ch_en1_filter_color_enhance", 500),
        # ("table2", 4500),  # 表格 纯净版
        # ("table2_enhance", 4500),  # 表格 降质版 default 5000
        # ("table_uvdoc", 4500),  # 表格 扭曲版 dafault 5000
        # ("nature_dataset", -1),  # 自然场景 纯净版
        # ("nature_dataset_enhance", -1),  # 自然场景 降质版 default -1
        # ("seal_dataset",5000),
        # ("seal_dataset_enhance", 5000),
        # ("seal_dataset_uvdoc", 5000),
        # ("idcard", 1000),
        # ("idcard_enhance", 1000),
        # ("Othercertificate_dataset",2000),
        # ("Othercertificate_dataset_enhance", 2000),
        # ("Othercertificate_dataset_uvdoc", 2000),

        # 水印数据集
        # ("watermark_removal_dataset/", 50000),

        # 保存表格线的数据集
        # ("table_cross_cell",1000),
        # ("table_cross_cell_enhance",1000)

        # 新增的六个数据集 - 方案1：保守配比
        # 纯净版数据 - 适量使用
        ("nature_colorblock_full_inpainting_dataset", 1000),  # 1500张中取1000张
        ("nature_colorblock_dataset", 1000),  # 1500张中取1000张
        ("nature_white_text_dataset", 1000),  # 2000张中取1000张
        # 增强版数据 - 少量使用
        ("nature_colorblock_full_inpainting_dataset_enhance", 500),  # 1500张中取500张
        ("nature_colorblock_dataset_enhance", 500),  # 1500张中取500张
        ("nature_white_text_dataset_enhance", 500),  # 2000张中取500张
        # 总计：4500张新数据
    ]
else:# 测试环境数据集
    dataset_names = [
        # 注释掉原有数据集配置
        # ("doc_ch_en1_filter", 10),  # pdf 纯净版
        # ("doc_ch_en1_filter2", 10),  # pdf 纯净版
        # ("doc_ch_en1_filter_enhance", 10),  # pdf 降质版
        # ("doc_ch_en1_filter2_enhance", 10),  # pdf 降质版
        # ("doc_ch_en1_filter2_uvdoc", 10),  # pdf 扭曲版
        # ("table", 10),  # 表格 纯净版
        # ("table_enhance", 10),  # 表格 降质版
        # ("table_uvdoc", 10),  # 表格 扭曲版
        # ("nature_dataset", 10),  # 自然场景 纯净版
        # ("nature_dataset_enhance", 10),  # 自然场景 降质版
        # ("Othercertificate_dataset", 10),  # 其他证件 default -1
        # ("Othercertificate_dataset_enhance", 10),  # 其他证件 降质版 default -1
        # ("Othercertificate_dataset_uvdoc", 10),  # 其他证件扭曲 default -1
        # ("watermark_removal_dataset/", 10),  # 水印数据集
        # ("table_cross_cell", 10),  # 表格线数据集
        # ("table_cross_cell_enhance", 10)

        # 新增的六个数据集 - 测试模式，每个数据集取10张
        ("nature_colorblock_full_inpainting_dataset", 10),
        ("nature_colorblock_full_inpainting_dataset_enhance", 10),
        ("nature_colorblock_dataset", 10),
        ("nature_colorblock_dataset_enhance", 10),
        ("nature_white_text_dataset", 10),
        ("nature_white_text_dataset_enhance", 10),
    ]

# 模式是自适应正方形裁切
mode='adaptive'
# 直接裁切最大外接矩形框
# mode='rect'
is_crop_bool=True # 判断是否需要对图片进行裁切 - 修改为True，需要裁切
print(f'{"需要根据mask位置裁切子图" if is_crop_bool else "不裁切子图，直接用原图就行"}')
# 数据划分比例
train_ratio = 0.95 #0.95  # 每个数据名划分95%作为训练集
val_ratio = 0.05 #0.05  # 每个数据名划分5%作为验证集

if product_mode:
    # 输出文件路径 - 修改为新的文件名，避免覆盖现有训练数据
    train_output_path = os.path.join(dataset_root, "train_crop_new_nature.jsonl")
    val_output_path = os.path.join(dataset_root, "val_crop_new_nature.jsonl")
else:
    # 输出文件路径 - 测试模式
    train_output_path = os.path.join(dataset_root, "train_crop_new_nature_debug.jsonl")
    val_output_path = os.path.join(dataset_root, "val_crop_new_nature_debug.jsonl")

# 初始化全局训练集和验证集列表
global_train_data = []
global_val_data = []
def single_dataset(dataset_root,dataset_name,train_ratio,select_num):
    dataset_dir = os.path.join(dataset_root, dataset_name)  # 在根目录下有数据集名

    # 获取所有图片路径
    print(f"[Info] 正在收集数据集 {dataset_name} 的图片路径...")
    all_source_image_paths = glob.glob(f'{dataset_dir}/im/**.png')
    # 如果没有图片路径，跳过当前数据集
    if len(all_source_image_paths) == 0:
        print(f"[Warning] 数据集 {dataset_name} 下未找到任何图片路径！")
        return ([],[])
    # 构建所有数据的字典
    print(f"[Info] 正在构建 {dataset_name} 的数据字典...")
    all_data = []
    for source_image_path in tqdm(all_source_image_paths, desc=f"Processing {dataset_name}"):
        img_name = os.path.basename(source_image_path)  # 文件名
        parts = source_image_path.split(os.sep)
        parts[-2] = 'gt'
        gt_image_path = os.path.sep.join(parts)
        parts[-2] = 'mask'
        mask_image_path = os.path.sep.join(parts)

        gt_image_path = Path(gt_image_path)
        mask_image_path = Path(mask_image_path)

        # 对图像进行裁切 通过mask获取到裁切区域
        im_image = Image.open(source_image_path)
        masks_image = Image.open(mask_image_path)
        gt_image = Image.open(gt_image_path)
        # # 数据不完善 直接删去
        if not os.path.exists(str(gt_image_path)) or not os.path.exists(str(mask_image_path)):
            continue

        # 对数据进行裁切
        if is_crop_bool:
            # 返回该图的裁切位置 max_crops 一张图切多少张子图
            crop_index_list = crop_by_mask_contours([im_image, masks_image, gt_image], mask_index=1, expand_ratio=4, max_crops=20, target_size=None, sampling=None, mode=mode)
            #裁剪用户图
            # crop_index_list = crop_by_mask_contours([im_image, masks_image], mask_index=1, expand_ratio=4,
            #                                         max_crops=20, target_size=None, sampling=None, mode=mode)
            for crop_index in crop_index_list:
                # 将绝对路径换成相对路径
                data = {
                    "dataset_name": dataset_name,
                    "im": str(Path(source_image_path).relative_to(dataset_dir)),
                    "mask": str(mask_image_path.relative_to(dataset_dir)),
                    "gt": str(gt_image_path.relative_to(dataset_dir)),
                    "crop_index": crop_index
                }
                all_data.append(data)
        else:
            w,h=im_image.size
            data={
                "dataset_name": dataset_name,
                "im": str(Path(source_image_path).relative_to(dataset_dir)),
                "mask": str(mask_image_path.relative_to(dataset_dir)),
                "gt": str(gt_image_path.relative_to(dataset_dir)),
                "crop_index": [0,0,w-1,h-1]
            }
            all_data.append(data)


    # 数据随机打乱
    random.shuffle(all_data)

    # 如果采样数量为-1，使用全部数据
    if select_num == -1 or select_num >= len(all_data):
        sampled_data = all_data
        # 如果需要重采样扩充
        if select_num > len(all_data):
            sampled_data = all_data * (select_num // len(all_data)) + random.sample(all_data, select_num % len(all_data))
    else:
        # 随机采样指定数量
        sampled_data = random.sample(all_data, select_num)

    # 数据再次随机打乱（确保采样后顺序无关）
    random.shuffle(sampled_data)

    # 按比例划分训练集和验证集
    train_size = int(len(sampled_data) * train_ratio)
    train_data = sampled_data[:train_size]
    val_data = sampled_data[train_size:]

    # 将当前数据集的训练集和验证集数据添加到全局列表
    print(f"[Info] 数据集 {dataset_name} 完成处理：")
    print(f"  - 总数据量：{len(all_data)}")
    print(f"  - 采样数据量：{len(sampled_data)}")
    print(f"  - 训练集数量：{len(train_data)}")
    print(f"  - 验证集数量：{len(val_data)}")
    return (train_data,val_data)
# 遍历每个数据集

with mp.Pool(processes=14) as pool:
    result=[pool.apply_async(single_dataset,(root_path,dataset_name[0],train_ratio,dataset_name[1])) for dataset_name in dataset_names] #在数据集根目录获取数据
    print('====开始收集多进程的结果========')
    for r in result:
        train_list=r.get()[0]
        val_list=r.get()[1]
        global_train_data.extend(train_list)
        global_val_data.extend(val_list)
# 全局数据打乱（训练数据和验证数据分别打乱）
random.shuffle(global_train_data)
random.shuffle(global_val_data)
print(f'训练集数量:{len(global_train_data)}')
print(f'验证集数量:{len(global_val_data)}')
# 一次性写入训练集文件
with open(train_output_path, 'w', encoding='utf-8') as train_file:
    for item in tqdm(global_train_data, desc="Writing train.jsonl"):
        train_file.write(json.dumps(item, ensure_ascii=False) + '\n')

# 一次性写入验证集文件
with open(val_output_path, 'w', encoding='utf-8') as val_file:
    for item in tqdm(global_val_data, desc="Writing val.jsonl"):
        val_file.write(json.dumps(item, ensure_ascii=False) + '\n')
print(f"[Info] 所有数据集处理完成，文件已生成：模式:{'正式' if product_mode else 'debug'}")
print(f"  - 训练集：{train_output_path}")
print(f"  - 验证集：{val_output_path}")