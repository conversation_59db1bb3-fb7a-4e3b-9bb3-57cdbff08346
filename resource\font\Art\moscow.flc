flc2a
# This controlfile maps the U.S. keyboard to Cyrillic
# in the style of the "moscow" font, but is meant for use
# with a FIGlet Unicode font with Cyrillic codes, such as "banner"
#
# "moscow" is an upper-case-only font, so for full compatibility
# use the "upper" controlfile as well and avoid "@" and "<".
t / \0x044c
t \0x5c \0x044a
t | \0x044b
t ~ \0x044d
t < \0x042d
t ` \0x0443
t @ \0x042e
t > \0x0439
t A \0x0410
t a \0x0430
t B \0x0411
t b \0x0431
t C \0x0427
t c \0x0447
t D \0x0414
t d \0x0434
t E \0x0415
t e \0x0435
t F \0x0424
t f \0x0444
t G \0x0413
t g \0x0433
t H \0x0425
t h \0x0445
t I \0x0418
t i \0x0438
t J \0x0416
t j \0x0436
t K \0x041a
t k \0x043a
t L \0x041b
t l \0x043b
t M \0x041c
t m \0x043c
t N \0x041d
t n \0x043d
t O \0x041e
t o \0x043e
t P \0x041f
t p \0x043f
t Q \0x0426
t q \0x0446
t R \0x0420
t r \0x0440
t S \0x0421
t s \0x0441
t T \0x0422
t t \0x0442
t U \0x0423
t u \0x0443
t V \0x0412
t v \0x0432
t W \0x0428
t w \0x0448
t X \0x0429
t x \0x0449
t Y \0x042f
t y \0x044f
t Z \0x0417
t z \0x0437
t = \-6
