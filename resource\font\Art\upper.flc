flc2a
# Upper by <PERSON> <<EMAIL>> 1/94
# Converts lower-case to upper-case.
# Modified to support all of Unicode by <PERSON> <<EMAIL>> 10/96
# figlet release 2.2 -- November 1996
#
# This is a figlet controlfile.
#
# Explanation of first line:
#   flc2 - "magic number" for file identification
#   a    - should always be `a', for now
#
# File body:
#   The first character of any line is the "command" for that line.
#   Possible figlet 2.1 commands are as follows:
#   `#' - comment, continues until end-of-line
#   `t' - translate, a not-so-fancy version of the Unix "tr" command
#   `f' - freeze, treat further lines as if they were in a new file
#
# The following lines convert lower-case to upper-case.
#
# Standard letters:
t a-z A-Z
#
# figlet Deutsch characters (obsolete in 2.2 and later):
t \-252-\-250 \-255-\-253
#
# ISO Latin-1 accented letters:
t \224-\246 \192-\214
t \248-\254 \216-\222
# Note: Latin-1 has no upper-case Y umlaut, so map to "Y".
# But remove the following line for Unicode fonts.
t \255 Y
# This is the proper Unicode mapping, and does no harm for non-Unicode fonts.
t \255 \0x0178
# Latin Extended A (Latin-2,3,4,5) accented letters:
t \0x01\0x01 \0x0100
t \0x0103 \0x0102
t \0x0105 \0x0104
t \0x0107 \0x0106
t \0x0109 \0x0108
t \0x010B \0x010A
t \0x010D \0x010C
t \0x010F \0x010E
t \0x01\0x01 \0x0100
t \0x0103 \0x0102
t \0x0105 \0x0104
t \0x0117 \0x0116
t \0x0119 \0x0118
t \0x011B \0x011A
t \0x011D \0x011C
t \0x011F \0x011E
t \0x01\0x01 \0x0100
t \0x0103 \0x0102
t \0x0105 \0x0104
t \0x0127 \0x0126
t \0x0129 \0x0128
t \0x012B \0x012A
t \0x012D \0x012C
t \0x012F \0x012E
t \0x0131 I
t \0x0133 \0x0132
t \0x0135 \0x0134
t \0x0137 \0x0136
t \0x0138 K
t \0x013A \0x0139
t \0x013C \0x013B
t \0x013E \0x013D
t \0x0140 \0x013F
t \0x0142 \0x0141
t \0x0144 \0x0143
t \0x0146 \0x0145
t \0x0148 \0x0147
t \0x0149 N
t \0x014B \0x014A
t \0x014D \0x014C
t \0x014F \0x014E
t \0x0151 \0x0150
t \0x0153 \0x0152
t \0x0155 \0x0154
t \0x0157 \0x0156
t \0x0159 \0x0158
t \0x015B \0x015A
t \0x015D \0x015C
t \0x015F \0x015E
t \0x0161 \0x0160
t \0x0163 \0x0162
t \0x0165 \0x0164
t \0x0167 \0x0166
t \0x0169 \0x0168
t \0x016B \0x016A
t \0x016D \0x016C
t \0x016F \0x016E
t \0x0171 \0x0170
t \0x0173 \0x0172
t \0x0175 \0x0174
t \0x0177 \0x0176
t \0x017A \0x0179
t \0x017C \0x017B
t \0x017E \0x017D
t \0x017F S
# Greek letters:
\0x037A \0x0399
\0x0390 \0x0399
\0x03AC \0x0386
\0x03AD \0x0388
\0x03AE \0x0389
\0x03AF \0x038A
\0x03B0 \0x03AB
\0x03B1-\0x03C1 \0x0391-\0x03A1
\0x03C2 \0x03A3
\0x03C3-\0x03CB \0x03A3-\0x03AB
\0x03CC \0x038C
\0x03CD \0x038E
\0x03CE \0x038F
\0x03E3 \0x03E2
\0x03E5 \0x03E4
\0x03E7 \0x03E6
\0x03E9 \0x03E8
\0x03EB \0x03EA
\0x03ED \0x03EC
\0x03EF \0x03ED
# Cyrillic letters:
\0x0430-\0x044F \0x0410-\0x042F
\0x0451-\0x045F \0x0401-\0x040F
\0x0461 \0x0460
\0x0463 \0x0461
\0x0465 \0x0464
\0x0467 \0x0466
\0x0469 \0x0468
\0x046B \0x046A
\0x046D \0x046C
\0x046F \0x046E
\0x0471 \0x0470
\0x0473 \0x0471
\0x0475 \0x0474
\0x0477 \0x0476
\0x0479 \0x0478
\0x047B \0x047A
\0x047D \0x047C
\0x047F \0x047E
\0x0481 \0x0480
\0x0491 \0x0490
\0x0493 \0x0491
\0x0495 \0x0494
\0x0497 \0x0496
\0x0499 \0x0498
\0x049B \0x049A
\0x049D \0x049C
\0x049F \0x049E
\0x04A1 \0x04A0
\0x04A3 \0x04A1
\0x04A5 \0x04A4
\0x04A7 \0x04A6
\0x04A9 \0x04A8
\0x04AB \0x04AA
\0x04AD \0x04AC
\0x04AF \0x04AE
\0x04B1 \0x04B0
\0x04B3 \0x04B1
\0x04B5 \0x04B4
\0x04B7 \0x04B6
\0x04B9 \0x04B8
\0x04BB \0x04BA
\0x04BD \0x04BC
\0x04BF \0x04BE
\0x04C2 \0x04C1
\0x04C4 \0x04C3
\0x04C8 \0x04C7
\0x04CC \0x04CB
\0x04D1 \0x04D0
\0x04D3 \0x04D2
\0x04D5 \0x04D4
\0x04D7 \0x04D6
\0x04D9 \0x04D8
\0x04DB \0x04DA
\0x04DD \0x04DC
\0x04DF \0x04DE
\0x04E1 \0x04E0
\0x04E3 \0x04E2
\0x04E5 \0x04E4
\0x04E7 \0x04E6
\0x04E9 \0x04E8
\0x04EB \0x04EA
\0x04ED \0x04EC
\0x04EF \0x04EE
\0x04F1 \0x04F0
\0x04F3 \0x04F2
\0x04F5 \0x04F4
\0x04F7 \0x04F6
\0x04F9 \0x04F8
# Armenian letters:
\0x0561-\0x0586 \0x0531-\0x0556
